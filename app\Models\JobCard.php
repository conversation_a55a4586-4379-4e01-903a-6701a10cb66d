<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JobCard extends Model
{
    protected $fillable = [
        'machine_id',
        'date_created',
        'service_type',
        'status_name',
        'status_color',
        'current_km_of_service',
        'next_km_of_service',
        'status_id'
    ];
    
    public function machine(){
        return $this->belongsTo(Machines::class,'machine_id');
    }
    
     public function status(){
        return $this->belongsTo(Status::class,'status_id');
    }
    
    public function maintainanceLogs(){
        return $this->hasMany(MaintainanceLog::class);
    }
    
     public function tyres(){
        return $this->hasMany(Tyre::class,'jobcard_id');
    }
    
     public function costNotes(){
        return $this->hasMany(CostNote::class,'jobcard_id');
    }
    
     public function spares(){
        return $this->hasMany(ItemUsage::class,'jobcard_id');
    }

    public function itemUsages(){
        return $this->hasMany(ItemUsage::class,'jobcard_id');
    }
    
    
    
}
