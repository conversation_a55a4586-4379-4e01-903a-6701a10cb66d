<?php

namespace App\Http\Controllers;

use App\Models\CostNote;
use App\Models\ItemUsage;
//04 august 2025
use App\Models\JobCard;
use App\Models\Tyre;
use App\Models\MaintainanceLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail as Mailer;
use App\Mail\copexMail;
use App\Models\Machine;

class CostNoteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
public function index(Request $request)
{
    if ($request->has('type')) {
        $data = CostNote::where('type', $request->type)->where('jobcard_id',$request->jobcard_id)->get();
    }elseif ($request->has('jobcard_id') && !$request->has('type')) {
        $data = CostNote::where('jobcard_id', $request->jobcard_id)->get();
    } else {
        $data = CostNote::all();
    }

    return response()->json($data);
}


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $CostNote = CostNote::create($request->all());

        return response()->json($CostNote, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(CostNote $CostNote)
    {
        return response()->json($CostNote);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CostNote $CostNote)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CostNote $CostNote)
    {
        $CostNote->update($request->all());

        return response()->json($CostNote);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CostNote $request, $CostNote)
    {
        $CostNote->update($request->all());;

        return response()->json(['message' => 'Job card deleted']);
    }
    
public function costNoteSummary(Request $request)
{
    $jobcardId = $request->jobcard_id;
    $tyres = Tyre::where('jobcard_id', $jobcardId)->get(); 
    $spares = ItemUsage::where('jobcard_id',$jobcardId)->where('usage_type','spares')->get();
    $body_works = ItemUsage::where('jobcard_id',$jobcardId)->where('usage_type','body_works')->get();
    $electrical_works = CostNote::where('jobcard_id', $jobcardId)->where('type', 'electric')->get();
    $labour = CostNote::where('jobcard_id', $jobcardId)->where('type', 'labour')->get();
    $engineering_works = CostNote::where('jobcard_id', $jobcardId)->where('type', 'engineering')->get();
    $additional_works = CostNote::where('jobcard_id', $jobcardId)->where('type', 'additional')->get();
     
    $tyres_cost = $tyres->sum(fn($item) =>  $item->rate);  
    $spares_cost = $spares->sum(fn($item) => $item->quantity * $item->rate);
    $body_works_cost = $body_works->sum(fn($item) => $item->quantity * $item->rate);
    $electrical_works_cost = $electrical_works->sum(fn($item) => $item->quantity * $item->rate);
    $labour_cost = $labour->sum(fn($item) => $item->quantity * $item->rate);
    $engineering_works_cost = $engineering_works->sum(fn($item) => $item->quantity * $item->rate);
    $additional_works_cost = $additional_works->sum(fn($item) => $item->quantity * $item->rate);

    $data = [
        'tyres'=>$tyres_cost,
        'spares' => $spares_cost,
        'body_works' => $body_works_cost,
        'electrical_works' => $electrical_works_cost,
        'labour' => $labour_cost,
        'engineering' => $engineering_works_cost,
        'additional' => $additional_works_cost,
        'total' => $spares_cost + $body_works_cost + $electrical_works_cost + $labour_cost + $engineering_works_cost + $additional_works_cost + $tyres_cost,
    ];

    return response()->json($data);
}

public function jobcardMail(Request $request){
    $jobcard_id = $request->jobcard_id;
   $jobcard = JobCard::with('machine.category')->where('id', $jobcard_id)->first();
$asset = [
    'asset_name' => $jobcard->machine->name ?? null,
    'type' => $jobcard->machine->type_name ?? null,
    'current_km_service' => $jobcard->current_km_of_service ?? null,
    'next_km_service' => $jobcard->next_km_of_service ?? null,
    'service_type' => $jobcard->service_type ?? null,
    'jobcard_id' => $jobcard->id ?? null,
    'date_created' => $jobcard->date_created ?? null,
    'status'=>$jobcard->status->name ??null
];

    $defects = MaintainanceLog::where('job_card_id',$jobcard_id)->where('issue_type','defect')->get();
    $works = MaintainanceLog::where('job_card_id',$jobcard_id)->where('issue_type','work_done')->get();
    $spares = ItemUsage::where('jobcard_id',$jobcard_id)->where('usage_type','spares')->get();
    $tyres = Tyre::where('jobcard_id',$jobcard_id)->get();
    $labour = CostNote::where('jobcard_id',$jobcard_id)->where('type','labour')->get();
    $body = ItemUsage::where('jobcard_id',$jobcard_id)->where('usage_type','body_works')->get();
    $electric = CostNote::where('jobcard_id',$jobcard_id)->where('type','electric')->get();
    $engineering = CostNote::where('jobcard_id',$jobcard_id)->where('type','engineering')->get();
    $additional = CostNote::where('jobcard_id',$jobcard_id)->where('type','additional')->get();
    
     
    $tyres_cost = $tyres->sum(fn($item) =>  $item->rate);  
    $spares_cost = $spares->sum(fn($item) => $item->quantity * $item->rate);
    $body_works_cost = $body->sum(fn($item) => $item->quantity * $item->rate);
    $electrical_works_cost = $electric->sum(fn($item) => $item->quantity * $item->rate);
    $labour_cost = $labour->sum(fn($item) => $item->quantity * $item->rate);
    $engineering_works_cost = $engineering->sum(fn($item) => $item->quantity * $item->rate);
    $additional_works_cost = $additional->sum(fn($item) => $item->quantity * $item->rate);

    $summary = [
        'tyres'=>$tyres_cost,
        'spares' => $spares_cost,
        'body_works' => $body_works_cost,
        'electrical_works' => $electrical_works_cost,
        'labour' => $labour_cost,
        'engineering' => $engineering_works_cost,
        'additional' => $additional_works_cost,
        'total' => $spares_cost + $body_works_cost + $electrical_works_cost + $labour_cost + $engineering_works_cost + $additional_works_cost + $tyres_cost,
    ];


 try {
        Mailer::to($request->receiver)->send(new \App\Mail\copexMail(
            compact('jobcard','defects','works','spares','tyres','labour','body','electric','engineering','additional','summary'),
            'jobcard_mail'
        ));

        return response()->json([
            'message' => 'Email sent successfully',
            'receiver'=>$request->receiver,
            'time' => now()->toDateTimeString(),
        
        ], 200);
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Email sending failed',
            'details' => $e->getMessage()
        ], 500);
    }
    
    // return view('jobcard_mail',compact('jobcard','defects','works','spares','tyres','labour','body','electric','engineering','additional','summary'));

// return response()->json($jobcard);
    
}




}
