<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;

class Report2 extends Controller
{
    public function generatePDF()
    {
        $data = [
            'title' => 'Welcome to Laravel PDF',
            'content' => 'This is a simple PDF generated using Laravel DomPDF.'
        ];
        
        $pdf = PDF::loadView('pdf.report2', $data);
        
        // return $pdf->download('report2.pdf');
        
        // Alternatively, you can use:
        return $pdf->stream('report1.pdf'); // To display in browser
        // return view('pdf.report1', $data);
    }
}