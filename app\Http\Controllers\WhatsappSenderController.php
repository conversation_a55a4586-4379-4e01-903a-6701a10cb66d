<?php

namespace App\Http\Controllers;

use App\Models\WhatsappSender;
use Illuminate\Http\Request;

class WhatsappSenderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $senders= whatsappSender::all();
        return response()->json($senders,200);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
 public function store(Request $request)
{
    $existing = WhatsappSender::where('phone', $request->phone)->first();

    if ($existing) {
        $existing->update(['message_id' => $request->message_id]);
        return response()->json('message id was updated ✔️');
    }

    WhatsappSender::create([
        'phone' => $request->phone,
        'message_id' => $request->message_id,
    ]);

    return response()->json('new message id captured ✔️');
}


    /**
     * Display the specified resource.
     */
    public function show(WhatsappSender $whatsappSender)
    {
        $sender = WhatsappSender::find($whatsappSender);
        return response()->json($sender);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(WhatsappSender $whatsappSender)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
  public function update(Request $request, WhatsappSender $whatsappSender)
{
    $whatsappSender->update($request->all());

    return response()->json($whatsappSender);
}

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(WhatsappSender $whatsappSender)
    {
        //
    }
}
