<?php

namespace App\Models;

use App\Models\Spares;
use Illuminate\Database\Eloquent\Model;

class StoreItem extends Model
{
    protected $fillable = [
        'store_id',
        'item_id',
        'quantity',
    ];
    
    public function spares() {
       return $this->belongsTo(ItemList::class,'item_id');
    }
    
    public function items(){
        return $this->belongsTo(ItemList::class,'item_id');
    }
         
}
