<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\ApprovalCheck;
use App\Models\PurchaseOrder;
use App\Models\WhatsappRegion;
use App\Models\Service;
use App\Models\Passenger;
use App\Models\ServiceDescription;
use App\Models\ServiceDescriptionCategory;
use App\Models\PaymentMethod;
use App\Models\WhatsappSender;
use App\Models\DroppingRegion;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Http;

class WhatsappMessageController extends Controller
{
public function getWhatsappAutoResponse(Request $request)
{
    $data = json_decode($request->getContent(), true);
    $senderWaId = $data['entry'][0]['changes'][0]['value']['contacts'][0]['wa_id'] ?? null;
    $senderName = $data['entry'][0]['changes'][0]['value']['contacts'][0]['profile']['name'] ?? 'Customer';
    $messageId = $data['entry'][0]['changes'][0]['value']['messages'][0]['id'] ?? null;
    
    
    if ($senderWaId && $messageId) {
        $existing = WhatsappSender::where('phone', $senderWaId)->first();

        if ($existing) {
            $existing->update(['message_id' => $messageId]);
        } else {
            WhatsappSender::create([
                'phone' => $senderWaId,
                'message_id' => $messageId,
            ]);
            
                $this->sendWhatsAppMessage([
    "messaging_product" => "whatsapp",
    "to" => $senderWaId,
    "type" => "text",
    "text" => ["body" => "Karibu Sana Mgeni"]
]);

        }
    }
    
    
    
    
    

    // 1️⃣ Handle "booking" command (first step)
    if (isset($data['entry'][0]['changes'][0]['value']['messages'][0]['text'])) {
        $messageContent = strtolower($data['entry'][0]['changes'][0]['value']['messages'][0]['text']['body']);
        
    if (str_starts_with($messageContent, 'seat:')) { 
         $seat = substr($messageContent, 5); // Remove the s_ prefix
        $passenger = Passenger::where('whatsapp_id', $senderWaId)->first();
       if ($passenger) {
                  $passenger->update(['seat'=>$seat]);
                }
          $paymentMethods = PaymentMethod::all();
    
    if ($paymentMethods->isEmpty()) {
        return $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "to" => $senderWaId,
            "type" => "text",
            "text" => ["body" => "⚠️ Hakuna njia za malipo zilizopatikana."]
        ]);
    }
    
    $paymentOptions = [];
    foreach ($paymentMethods as $method) {
        $paymentOptions[] = [
            "id" => "payment_" . $method->id,
            "title" => $method->title,
            "description" => "" // No short_description assumed
        ];
    }
    
     $passenger = Passenger::where('whatsapp_id', $senderWaId)->first();
    // Combine confirmation + payment options in ONE interactive message
    $this->sendWhatsAppMessage([
        "messaging_product" => "whatsapp",
        "to" => $senderWaId,
        "type" => "interactive",
        "interactive" => [
            "type" => "list",
            "body" => [
                "text" => "✅ Umefanikiwa  Kujaza taarifa za  Ticket\n\n"
                    . "👤 Jina: $senderName\n"
                    . "🏡 Kutoka : $passenger->source\n"
                    . "🚌 Kwenda : $passenger->destination\n"
                    . "🔖 Safari : $passenger->departure_date\n"
                    . "🎚Daraja: $passenger->bus_class\n"
                    . "💺 Siti Namba : $passenger->seat\n\n"
                    . "Tafadhali chagua njia ya malipo:\n\n"
            ],
            "action" => [
                "button" => "Njia za Malipo",
                "sections" => [
                    [
                        "title" => "Njia za Malipo",
                        "rows" => $paymentOptions
                    ]
                ]
            ],
            "footer" => [
                "text" => "Asante kwa kuwa mteja wetu."
            ]
        ]
    ]);
        
          
    }elseif (preg_match('/(enx|asant|asante|thanks|thank you|shukran|thnx|tanx)/i', $messageContent)) {
            $this->sendWhatsAppMessage([
                "messaging_product" => "whatsapp",
                "to" => $senderWaId,
                "type" => "text",
                "text" => ["body" => "Karibu tena"]
            ]);
        }elseif (preg_match('/(taa|bulb|light|mwanga)/i', $messageContent)) {
    $response = Http::get('https://trustdigital.space/api/iot/lights/1');
    if ($response->successful()) {
        $status = $response->json('data.status');
        $message = $status == 1 ? "Light is ON" : "Light is OFF";

        // Send button reply to toggle light
        $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "recipient_type" => "individual",
            "to" => $senderWaId,
            "type" => "interactive",
            "interactive" => [
                "type" => "button",
                "body" => [
                    "text" => $message
                ],
                "action" => [
                    "buttons" => [
                        [
                            "type" => "reply",
                            "reply" => [
                                "id" => $status == 1 ? "light_0" : "light_1",
                                "title" => $status == 1 ? "Switch Off" : "Switch On"
                            ]
                        ]
                    ]
                ]
            ]
        ]);
    } else {
        $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "to" => $senderWaId,
            "type" => "text",
            "text" => ["body" => "Failed to fetch light status"]
        ]);
    }
}

// Handle button click elsewhere
elseif (str_starts_with($selectedButton, 'light_')) {
    $status = explode('_', $selectedButton)[1];

    $update = Http::put('https://trustdigital.space/api/iot/lights/1', [
        'status' => (int) $status
    ]);

    if ($update->successful()) {
        $message = $status == 1 ? "Light is now ON" : "Light is now OFF";
    } else {
        $message = "Failed to change light status.";
    }

    $this->sendWhatsAppMessage([
        "messaging_product" => "whatsapp",
        "recipient_type" => "individual",
        "to" => $senderWaId,
        "type" => "interactive",
        "interactive" => [
            "type" => "button",
            "body" => [
                "text" => $message
            ],
            "action" => [
                "buttons" => [
                    [
                        "type" => "reply",
                        "reply" => [
                            "id" => $status == 1 ? "light_0" : "light_1",
                            "title" => $status == 1 ? "Switch Off" : "Switch On"
                        ]
                    ]
                ]
            ]
        ]
    ]);

}

        else{
        
        
       
        // check whether user exists in the database
       $this->sendWhatsAppMessage([
        "messaging_product" => "whatsapp",
        "status" => "read",
        "to" => $senderWaId,
        "message_id" => $data['entry'][0]['changes'][0]['value']['messages'][0]['id'],
        "typing_indicator" => ["type" => "text"]
    ]);
    
  
    $passenger = Passenger::where('whatsapp_id', $senderWaId)->first();

    if (!$passenger) {
        // Create new passenger if doesn't exist
        $passenger = Passenger::create([
            'whatsapp_id' => $senderWaId,
            'name' => $senderName,
        ]);
        
          //dozzer comment
           $response = $this->callGeminiAPI($messageContent );

$this->sendWhatsAppMessage([
    "messaging_product" => "whatsapp",
    "to" => $senderWaId,
    "type" => "text",
    "text" => ["body" => $response]
]);
           return ;
    
          $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "to" => $senderWaId,
            "type" => "text",
            "text" => ["body" =>  "Habari $senderName! Karibu sana free tier ticket Booking .\n Sasa unaweza kuanza kufanya Booking."]
        ]);

    } else {
        
          //dozzer comment
          $response = $this->callGeminiAPI($messageContent );

$this->sendWhatsAppMessage([
    "messaging_product" => "whatsapp",
    "to" => $senderWaId,
    "type" => "text",
    "text" => ["body" => $response]
]);

          return ;
          
          $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "to" => $senderWaId,
            "type" => "text",
            "text" => ["body" =>  "Habari 👋 $senderName! karibu tena free tier ticket booking \n\n Tumekukumbuka mteja wetu ..."]
            
        ]);
    }
        // check user existing ends
        
          $regions = WhatsappRegion::select('id', 'title')->get();
            $rows = $regions->map(function ($region) {
                return [
                    "id" => "s_" . $region->id, 
                    "title" => $region->title
                ];
            })->toArray();
            
            $interactivePayload = [
                "messaging_product" => "whatsapp",
                "to" => $senderWaId,
                "type" => "interactive",
                "interactive" => [
                    "type" => "list",
                    "body" => ["text" => "Tafadhali chagua Mji Uliopo:"],
                    "action" => [
                        "button" => "Mji Uliopo",
                        "sections" => [[
                            "title" => "Miji Iliyopo",
                            "rows" => $rows
                        ]]
                    ]
                ]
            ];
            
            $this->sendWhatsAppMessage($interactivePayload);
        }
        
    }
    
    // 2️⃣ Handle region selection (source selection)
    if (isset($data['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['list_reply']['id'])) {
        $selectedId = $data['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['list_reply']['id'];
        $selectedSource = $data['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['list_reply']['title'];
        
        // Check if this is a source (region) selection
        if (str_starts_with($selectedId, 's_')) {
            $regionId = substr($selectedId, 2); // Remove the s_ prefix
            
            
               $passenger = Passenger::where('whatsapp_id', $senderWaId)->first();
             if ($passenger) {
                  $passenger->update(['source_id' => $regionId,'source'=>$selectedSource]);
                }
                
            $selectedRegion = WhatsappRegion::find($regionId);
            // Fetch destinations for this region
            $destinations = DroppingRegion::where('boarding_region', $regionId)
                ->select('id', 'title')
                ->get();
                
            $rows = $destinations->map(function ($destination) {
                return [
                    "id" => "date_" . $destination->id,  // Prefix with d_ for destination
                    "title" => $destination->title
                ];
            })->toArray();
            
            if (!empty($rows)) {
                $interactivePayload = [
                    "messaging_product" => "whatsapp",
                    "to" => $senderWaId,
                    "type" => "interactive",
                    "interactive" => [
                        "type" => "list",
                        "body" => ["text" => "Tafadhali Chagua Mji Uendao kutokea $selectedSource :\n\n"],
                        "action" => [
                            "button" => "Miji Iliyopo",
                            "sections" => [[
                                "title" => "Mji Uendao",
                                "rows" => $rows
                            ]]
                        ]
                    ]
                ];
                
                $this->sendWhatsAppMessage($interactivePayload);
            } else {
                $this->sendWhatsAppMessage([
                    "messaging_product" => "whatsapp",
                    "to" => $senderWaId,
                    "type" => "text",
                    "text" => ["body" => "🚫 Hakuna Safari kwa Mji huu kwasasa Jaribu tena Badae.Asante."]
                ]);
            }
        }
      $selectedButton = $data['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['button_reply']['id'];
if (str_starts_with($selectedButton, 'light_')) {
    // Update or get status...

    $nextMessage = "Light toggled. What next?";

    $this->sendWhatsAppMessage([
        "messaging_product" => "whatsapp",
        "to" => $senderWaId,
        "type" => "text",
        "text" => ["body" => $nextMessage]
    ]);
}

        // Check if this is a destination selection
// DATE SELECTION
elseif (str_starts_with($selectedId, 'date_')) {
        $selectedDestination = $data['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['list_reply']['title'];
               $passenger = Passenger::where('whatsapp_id', $senderWaId)->first();
             if ($passenger) {
                  $passenger->update(['destination'=>$selectedDestination]);
                }
                
    $rows = [];
for ($i = 0; $i < 7; $i++) {
    $date = now()->addDays($i);
    
    if ($i == 0) {
        $title = "Leo : " . $date->format('Y-m-d');
    } elseif ($i == 1) {
        $title = "Kesho : " . $date->format('Y-m-d');
    } elseif ($i == 2) {
        $title = "Kesho kutwa: " . $date->format('Y-m-d');
    } else {
        $title = $date->format('D : Y-m-d');
    }

    $rows[] = [
        'id' => 'class_' . $date->format('Y-m-d'),
        'title' => $title
    ];
}

    
    $this->sendWhatsAppMessage([
        "messaging_product" => "whatsapp",
        "to" => $senderWaId,
        "type" => "interactive",
        "interactive" => [
            "type" => "list",
            "body" => ["text" => "Tafadhali Chagua Tarehe ya safari :\n\n"],
            "action" => [
                "button" => "Tarehe Ya Safari",
                "sections" => [[
                    "title" => "Tarehe Ya Safari",
                    "rows" => $rows
                ]]
            ]
        ]
    ]);
}

// CLASS SELECTION (TRIGGERED AFTER DATE IS CHOSEN)
elseif (str_starts_with($selectedId, 'class_')) {
      $selectedDate = $data['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['list_reply']['id'];
       $extractedDate = substr($selectedDate, 6); 
               $passenger = Passenger::where('whatsapp_id', $senderWaId)->first();
             if ($passenger) {
                  $passenger->update(['departure_date'=>$extractedDate]);
                }
    
    $dateId = explode('_', $selectedId)[1]; // Extract the date number
    
    $this->sendWhatsAppMessage([
        "messaging_product" => "whatsapp",
        "to" => $senderWaId,
        "type" => "interactive",
        "interactive" => [
            "type" => "list",
            "body" => ["text" => "Chagua Aina ya Basi kwa safari ya $extractedDate :\n\n"],
            "action" => [
                "button" => "Aina Ya Basi",
                "sections" => [[
                    "title" => "Aina Ya Basi",
                    "rows" => [
                        ['id' => "seats_normal", 'title' => "NORMAL : 50,000/="],
                        ['id' => "seats_vip", 'title' => "VIP : 60,000/="],
                        ['id' => "seats_vvip", 'title' => "VVIP : 70,000/="]
                    ]
                ]]
            ]
        ]
    ]);
} 


elseif (str_starts_with($selectedId, 'seats_')) {
    
          $busClass = $data['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['list_reply']['title'];
        $passenger = Passenger::where('whatsapp_id', $senderWaId)->first();
       if ($passenger) {
                  $passenger->update(['bus_class'=>$busClass]);
                }
    $dateId = explode('_', $selectedId)[1]; // Extract the date number
    
     $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "to" => $senderWaId,
            "type" => "image",
            "image" => [
                "link" => 'https://bcftrack.trustdigital.space/seats.jpg',
                "caption" => "Chagua siti Kwa Kuandika namba ya siti kwa utaratibu huu👇 \n\nseat:23"
            ]
        ]);
} 

       elseif (str_starts_with($selectedId, 'bus_')) {
    // Handle category selection
  
    // Prepare payment methods
    $paymentMethods = PaymentMethod::all();
    
    if ($paymentMethods->isEmpty()) {
        return $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "to" => $senderWaId,
            "type" => "text",
            "text" => ["body" => "⚠️ Hakuna njia za malipo zilizopatikana."]
        ]);
    }
    
    $paymentOptions = [];
    foreach ($paymentMethods as $method) {
        $paymentOptions[] = [
            "id" => "payment_" . $method->id,
            "title" => $method->title,
            "description" => "" // No short_description assumed
        ];
    }
    
    // Combine confirmation + payment options in ONE interactive message
    $this->sendWhatsAppMessage([
        "messaging_product" => "whatsapp",
        "to" => $senderWaId,
        "type" => "interactive",
        "interactive" => [
            "type" => "list",
            "body" => [
                "text" => "✅ Umefanikiwa  Kujaza taarifa za  Ticket\n\n"
                    . "👤 Jina:\n"
                    . "🏡 Kutoka:\n"
                    . "🚌 Kwenda:\n"
                    . "🔖 Tarehe ya safari:\n"
                    . "🎚Daraja:\n"
                    . "💰 Gharama:\n"
                    . "Tafadhali chagua njia ya malipo:"
            ],
            "action" => [
                "button" => "Njia za Malipo",
                "sections" => [
                    [
                        "title" => "Njia za Malipo",
                        "rows" => $paymentOptions
                    ]
                ]
            ],
            "footer" => [
                "text" => "Asante kwa kuwa mteja wetu."
            ]
        ]
    ]);
}

elseif (str_starts_with($selectedId, 'payment_')) {
    $paymentMethodId = substr($selectedId, 8);
    $selectedPaymentMethod = PaymentMethod::find($paymentMethodId);
    $paymentDetails = "💰 *MAELEZO YA MALIPO* 💰\n\n";
    $paymentDetails .= "🔹 *Njia:* " . $selectedPaymentMethod->title . "\n\n";
    $paymentDetails .= "📝 *Maelezo:*\n" . $selectedPaymentMethod->description . "\n\n";
    $paymentDetails .= "🔢 *Namba ya biashara:* `" . $selectedPaymentMethod->code . "`\n\n";
    $paymentDetails .= "⏱ Tupo tayari kukuhudumia masaa 24. Asante! Sana";
        $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "to" => $senderWaId,
            "type" => "document",
            "document" => [
                "link" => "https://bcftrack.trustdigital.space/api/v1/ticket-invoice/$senderWaId",
                "filename" => "Ticket Invoice Document",
                "caption" => $paymentDetails
            ]
        ]);
}

}
    
    
    return response()->json(['status' => 'success']);
}


public function servicePrototype(Request $request)
{
    $data = json_decode($request->getContent(), true);
    $senderWaId = $data['entry'][0]['changes'][0]['value']['contacts'][0]['wa_id'] ?? null;
    $senderName = $data['entry'][0]['changes'][0]['value']['contacts'][0]['profile']['name'] ?? 'Customer';
    
    if (isset($data['entry'][0]['changes'][0]['value']['messages'][0]['text'])) {
        $messageContent = strtolower($data['entry'][0]['changes'][0]['value']['messages'][0]['text']['body']);
        
        // Check for thank you messages
        if (preg_match('/(enx|asant|asante|thanks|thank you|shukran|thnx|tanx)/i', $messageContent)) {
            $this->sendWhatsAppMessage([
                "messaging_product" => "whatsapp",
                "to" => $senderWaId,
                "type" => "text",
                "text" => ["body" => "Karibu tenao"]
            ]);
        }
        
        // Check for electronics keyword
        elseif (strpos($messageContent, 'electronics') !== false) {
            $services = Service::select('id', 'title')->get();
            
            $rows = $services->map(function ($service) {
                return [
                    "id" => "s_" . $service->id,
                    "title" => $service->title
                ];
            })->toArray();
            
            $interactivePayload = [
                "messaging_product" => "whatsapp",
                "to" => $senderWaId,
                "type" => "interactive",
                "interactive" => [
                    "type" => "list",
                    "body" => ["text" => "Karibu Juma SmartPhones 📱 and Electronics\n\nChagua aina ya bidhaa unayotaka:\n"],
                    "action" => [
                        "button" => "Chagua Bidhaa",
                        "sections" => [[
                            "title" => "Aina za Bidhaa",
                            "rows" => $rows
                        ]]
                    ]
                ]
            ];
            
            $this->sendWhatsAppMessage($interactivePayload);
        }
    }
    // 2️⃣ Handle descriptions available (second step)
    elseif (isset($data['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['list_reply']['id'])) {
        $selectedId = $data['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['list_reply']['id'];
        
        // Service selection
        if (str_starts_with($selectedId, 's_')) {
            $serviceId = substr($selectedId, 2);
            $selectedService = Service::find($serviceId);
            
            if (!$selectedService) {
                return $this->sendWhatsAppMessage([
                    "messaging_product" => "whatsapp",
                    "to" => $senderWaId,
                    "type" => "text",
                    "text" => ["body" => "⚠️ Huduma hiyo haipo. Tafadhali chagua tena."]
                ]);
            }
            
            $descriptions = ServiceDescription::where('service_id', $serviceId)
                ->select('id', 'title')
                ->get();
                
            $rows = $descriptions->map(function ($description) {
                return [
                    "id" => "d_" . $description->id,
                    "title" => $description->title
                ];
            })->toArray();
            
            if (!empty($rows)) {
                $interactivePayload = [
                    "messaging_product" => "whatsapp",
                    "to" => $senderWaId,
                    "type" => "interactive",
                    "interactive" => [
                        "type" => "list",
                        "body" => ["text" => "Chagua maelezo zaidi ya bidhaa:"],
                        "action" => [
                            "button" => "Chagua Brand",
                            "sections" => [[
                                "title" => "Maelezo ya Bidhaa",
                                "rows" => $rows
                            ]]
                        ]
                    ]
                ];
                
                $this->sendWhatsAppMessage($interactivePayload);
            } else {
                $this->sendWhatsAppMessage([
                    "messaging_product" => "whatsapp",
                    "to" => $senderWaId,
                    "type" => "text",
                    "text" => ["body" => "🚫 Hakuna maelezo ya bidhaa kwa sasa. Tutaweka hivi punde.\n\nAsante kwa kusubiri."]
                ]);
            }
        }
        // Description selection
        elseif (str_starts_with($selectedId, 'd_')) {
            $descriptionId = substr($selectedId, 2);
            $selectedDescription = ServiceDescription::find($descriptionId);
            
            if (!$selectedDescription) {
                return $this->sendWhatsAppMessage([
                    "messaging_product" => "whatsapp",
                    "to" => $senderWaId,
                    "type" => "text",
                    "text" => ["body" => "⚠️ Maelezo hayapo. Tafadhali chagua tena."]
                ]);
            }
            
            $categories = ServiceDescriptionCategory::where('service_description_id', $descriptionId)
                ->select('id', 'title')
                ->get();
                
            $rows = $categories->map(function ($category) {
                return [
                    "id" => "c_" . $category->id,
                    "title" => "$category->title"
                ];
            })->toArray();
            
            if (!empty($rows)) {
                $interactivePayload = [
                    "messaging_product" => "whatsapp",
                    "to" => $senderWaId,
                    "type" => "interactive",
                    "interactive" => [
                        "type" => "list",
                        "body" => ["text" => "Chagua sifa ya bidhaa unayotaka:"],
                        "action" => [
                            "button" => "Chagua Sifa",
                            "sections" => [[
                                "title" => "Sifa za Bidhaa",
                                "rows" => $rows
                            ]]
                        ]
                    ]
                ];
                
                $this->sendWhatsAppMessage($interactivePayload);
            } else {
                $this->sendWhatsAppMessage([
                    "messaging_product" => "whatsapp",
                    "to" => $senderWaId,
                    "type" => "text",
                    "text" => ["body" => "🚫 Hakuna sifa za bidhaa kwa sasa. Tutaweka hivi punde.\n\nAsante kwa kusubiri."]
                ]);
            }
        }
       elseif (str_starts_with($selectedId, 'c_')) {
    // Handle category selection
    $categoryId = substr($selectedId, 2);
    $selectedCategory = ServiceDescriptionCategory::find($categoryId);
    
    if (!$selectedCategory) {
        return $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "to" => $senderWaId,
            "type" => "text",
            "text" => ["body" => "⚠️ Sifa hiyo haipo. Tafadhali chagua tena."]
        ]);
    }
    
    // Get service details
    $description = ServiceDescription::where('id', $selectedCategory->service_description_id)->first();
    $service = Service::where('id', $description->service_id)->first();
    
    // Prepare payment methods
    $paymentMethods = PaymentMethod::all();
    
    if ($paymentMethods->isEmpty()) {
        return $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "to" => $senderWaId,
            "type" => "text",
            "text" => ["body" => "⚠️ Hakuna njia za malipo zilizopatikana."]
        ]);
    }
    
    $paymentOptions = [];
    foreach ($paymentMethods as $method) {
        $paymentOptions[] = [
            "id" => "pm_" . $method->id,
            "title" => $method->title,
            "description" => "" // No short_description assumed
        ];
    }
    
    // Combine confirmation + payment options in ONE interactive message
    $this->sendWhatsAppMessage([
        "messaging_product" => "whatsapp",
        "to" => $senderWaId,
        "type" => "interactive",
        "interactive" => [
            "type" => "list",
            "body" => [
                "text" => "✅ Umefanikiwa kuchagua bidhaa:\n\n"
                    . "👤 Jina: $senderName\n\n"
                    . "📦 Bidhaa: {$service->title}\n"
                    . "📱 Brand: {$description->title}\n"
                    . "🔖 Sifa: {$selectedCategory->title}\n\n"
                    . "Tafadhali chagua njia ya malipo:"
            ],
            "action" => [
                "button" => "Njia za Malipo",
                "sections" => [
                    [
                        "title" => "Njia za Malipo",
                        "rows" => $paymentOptions
                    ]
                ]
            ],
            "footer" => [
                "text" => "Asante kwa kuwa mteja wetu."
            ]
        ]
    ]);
} elseif (str_starts_with($selectedId, 'pm_')) {
    $paymentMethodId = substr($selectedId, 3);
    $selectedPaymentMethod = PaymentMethod::find($paymentMethodId);
    $paymentDetails = "💰 *MAELEZO YA MALIPO* 💰\n\n";
    $paymentDetails .= "🔹 *Njia:* " . $selectedPaymentMethod->title . "\n\n";
    $paymentDetails .= "📝 *Maelezo:*\n" . $selectedPaymentMethod->description . "\n\n";
    $paymentDetails .= "🔢 *Namba ya biashara:* `" . $selectedPaymentMethod->code . "`\n\n";
    $paymentDetails .= "⏱ Tupo tayari kukuhudumia masaa 24. Asante! Sana";
        $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "to" => $senderWaId,
            "type" => "image",
            "image" => [
                "link" => $selectedPaymentMethod->logo,
                "caption" => $paymentDetails
            ]
        ]);
}
}
    

    return response()->json(['status' => 'success']);
}

// Helper function to avoid repeating API calls
private function sendWhatsAppMessage(array $payload)
{
    $client = new \GuzzleHttp\Client();
    $endpoint = 'https://graph.facebook.com/v22.0/721816217672551/messages';
    $accessToken = 'EAAXZAN9ZAW4VoBOZBhohedZA7pfKZBfzMghywTumPW7yFY0ZAI7u6Kh9DFtai4usEnmKsOnMZBYisZAQ0ojZBtEpDC72XXNyf6qFmtFtTCX61hv3I2eZCdZAevM9ahsDXGcIhT8stZAoTGstgOsujK4G1pxcSGqwFxiucZBcLr8HUoRxTLlB0IdVlLLlRGQ7n831T93WGUQZDZD'; // 🔴 Replace with env('WHATSAPP_TOKEN')

    $client->post($endpoint, [
        'headers' => [
            'Authorization' => 'Bearer ' . $accessToken,
            'Content-Type' => 'application/json',
        ],
        'body' => json_encode($payload)
    ]);
}
    
    public function getWhatsappController(){
        $data = WhatsappRegion::get();
        return response()->json($data);
    }
    
        public function index()
    {
    $whatsappRegions = PaymentMethod::select('id', 'title')->get();
        return response()->json(['message'=>'success','data'=>$whatsappRegions], 200);
    }

    
    public function store(Request $request){
           $whatsappMessage = WhatsappRegion::create($request->all());
            return response()->json($whatsappMessage, 201);
    }
    
      public function droppingRegion()
    {
    $droppingRegions = DroppingRegion::select('id', 'title')->get();
        return response()->json(['message'=>'success','data'=>$droppingRegions], 200);
    }
    
    public function ticketInvoice($senderWaId){
        $orderId = 340;
                $constants = [
            'message' => 'Ticket  PDF',
            'phone_1'=>'0700 000 000 ',
            'phone_2'=>'0600 000 000',
            'heading'=>'TICKET INVOICE',
            'city'=>'Mbezi',
            'po_box'=>'P.O.BOX 000, Dar-es-salaam',
            'tin'=>'000-000-000',
            'vrn'=>'A0A-A0A-A0A',
    ]; 
    $ticket = Passenger::where('whatsapp_id', $senderWaId)->first();
        $pdf = Pdf::loadView('invoice_pdf', compact(['constants','ticket']));
        return $pdf->stream('printout.pdf');
    }
    
    
    private function callGeminiAPI($prompt) {
    $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=AIzaSyAuGP7TxA6KPTmzSV3IF8WhTfzIKUfygHU';
    
    $data = [
        'contents' => [
            [
                'parts' => [
                    ['text' => $prompt]
                ]
            ]
        ]
    ];
    
    $options = [
        'http' => [
            'header'  => "Content-Type: application/json\r\n",
            'method'  => 'POST',
            'content' => json_encode($data),
        ],
    ];
    
    $context  = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    if ($result === FALSE) {
        return "Samahani, sijapata jibu la swali lako.";
    }
    
    $responseData = json_decode($result, true);
    
    // Extract the response text - this path might need adjustment based on actual API response structure
    return $responseData['candidates'][0]['content']['parts'][0]['text'] ?? "Samahani, sijaelewa jibu lililotumwa.";
}
    
}