<?php

namespace App\Http\Controllers;

use App\Models\WhatsappTemplate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class WhatsappTemplateController extends BaseController
{
    protected string $modelClass = WhatsappTemplate::class;

    protected function validateRequest(Request $request, ?Model $model = null): array
    {
        // Accept all input without validation
        return $request->all();
    }
}
