<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MachineFuelUsage extends Model
{
    protected $fillable = [
        'machine_id',
        'item_id',
        'project_id',
        'distribution_id',
        'bulk_id',
        'fuel_quantity',
        'ratio_factor',
        'driver_id',
        'driver_name',
        'supervisor_id',
        'supervisor_name',
        'usage_date',
        'task',
        'initial_reading',
        'final_reading',
    ];

    public function machines()
{
    return $this->belongsTo(Machines::class,'machine_id');
}

    public function project()
{
    return $this->belongsTo(Project::class,'project_id');
}

    public function bulk()
{
    return $this->belongsTo(Item::class,'bulk_id');
}

    public function itemList()
{
    return $this->belongsTo(ItemList::class,'item_id');
}

}
