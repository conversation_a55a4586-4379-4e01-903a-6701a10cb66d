<?php

namespace App\Http\Controllers;

use App\Models\Project;
use Illuminate\Http\Request;

class ProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json(Project::all());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $Project = Project::create($request->all());

        return response()->json($Project, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Project $Project)
    {
        return response()->json($Project);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Project $Project)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Project $Project)
    {
        $Project->update($request->all());

        return response()->json($Project);
    }

    /**
     * Remove the specified resource from storage.
     */
    // public function destroy(Project $Project)
    // {
    //     $Project->delete();

    //     return response()->json(['message' => 'Project deleted']);
    // }
}
