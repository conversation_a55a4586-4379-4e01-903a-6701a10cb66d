<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PurchaseOrder extends Model
{
    protected $fillable = [
        'company_id',
        'source',
        'source_id',
        'user_id',
        'requested_by',
        'vendor_id',
        'date_created',
        'project_id',
        'payment_terms',
        'status_name',
        'status_color',
        'deleted',
        'deleted_date',
        'deleted_reason',
        'deleted_by'
    ];
    
    public function items()
{
    return $this->hasMany(Item::class, 'order_id');
}

  public function company()
{
    return $this->belongsTo(Company::class, 'company_id');
}

  public function project()
{
    return $this->belongsTo(Project::class, 'project_id');
}

  public function vendor()
{
    return $this->belongsTo(Vendor::class, 'vendor_id');
}

  public function approvalCheck()
{
    return $this->hasMany(ApprovalCheck::class, 'purchase_order_id');
}
  public function grn(){
      return $this -> hasOne(GoodReceivedNote::class,'order_id');
  }  
}
