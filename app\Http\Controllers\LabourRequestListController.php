<?php

namespace App\Http\Controllers;

use App\Models\LabourRequestList;
use Illuminate\Http\Request;

class LabourRequestListController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {

    $query = LabourRequestList::query();        
    if ($request->has('request_type_id')) {
        $query->where('labour_request_type_id', $request->request_type_id);
    }
        return response()->json($query->get());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $LabourRequestList = LabourRequestList::create($request->all());

        return response()->json($LabourRequestList, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(LabourRequestList $LabourRequestList)
    {
        return response()->json($LabourRequestList);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(LabourRequestList $LabourRequestList)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, LabourRequestList $LabourRequestList)
    {
        $LabourRequestList->update($request->all());

        return response()->json($LabourRequestList);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(LabourRequestList $LabourRequestList)
    {
        $LabourRequestList->delete();

        return response()->json(['message' => 'Item list deleted']);
    }
}
