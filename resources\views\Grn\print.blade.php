<!DOCTYPE html>
<html>
<head>
    <title>GRN #{{ $grn->id }}</title>
    <style>
        body { font-family: Arial, sans-serif; }
        .header { text-align: center; margin-bottom: 20px; }
        .title { font-size: 18px; font-weight: bold; }
        .details { margin-bottom: 30px; }
        .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .table th { background-color: #f2f2f2; }
        .signature { margin-top: 50px; }
        .footer { margin-top: 30px; font-size: 12px; text-align: center; }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">GOODS RECEIVED NOTE (GRN)</div>
        <div>GRN #: {{ $grn->id }}</div>
        <div>Order #: {{ $grn->order_id }}</div>
    </div>

    <div class="details">
        <div><strong>Received By:</strong> {{ $grn->receiver->name }}</div>
        <div><strong>Date:</strong> {{ \Carbon\Carbon::parse($grn->created_at)->format('d/m/Y H:i') }}</div>
        <div><strong>Remarks:</strong> {{ $grn->remarks }}</div>
    </div>

    <table class="table">
        <thead>
            <tr>
                <th>#</th>
                <th>Item Description</th>
                <th>Category</th>
                <th>Quantity Ordered</th>
                <th>Quantity Received</th>
                <th>Shortage</th>
                <th>Excess</th>
                <th>Unit</th>
                <th>Rate</th>
                <th>Amount</th>
            </tr>
        </thead>
        <tbody>
            @foreach($grn->items as $index => $item)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td>{{ $item->grn_items->description }}</td>
                <td>{{ $item->grn_items->category }}</td>
                <td>{{ $item->grn_items->quantity }}</td>
                <td>{{ $item->grn_items->quantity - $item->shortage + $item->excess }}</td>
                <td>{{ $item->shortage }}</td>
                <td>{{ $item->excess }}</td>
                <td>{{ $item->grn_items->unit_measure }}</td>
                <td>{{ number_format($item->grn_items->rate, 2) }}</td>
                <td>{{ number_format(($item->grn_items->quantity - $item->shortage + $item->excess) * $item->grn_items->rate, 2) }}</td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <div class="signature">
        <div style="float: left; width: 40%;">
            <div style="border-top: 1px solid #000; width: 80%; margin-bottom: 5px;"></div>
            <div>Received By</div>
        </div>
        <div style="float: right; width: 40%;">
            <div style="border-top: 1px solid #000; width: 80%; margin-bottom: 5px;"></div>
            <div>Authorized By</div>
        </div>
        <div style="clear: both;"></div>
    </div>

    <div class="footer">
        Generated on {{ \Carbon\Carbon::now()->format('d/m/Y H:i') }}
    </div>
</body>
</html>