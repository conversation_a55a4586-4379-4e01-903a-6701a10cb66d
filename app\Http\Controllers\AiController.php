<?php

namespace App\Http\Controllers;

use App\Models\Ai;
use App\Models\ArtificialIntelligenceTraining;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
class AiController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $ai = Ai::with('instructions')->get();
        return response()->json($ai);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $ai = Ai::create($request->all());
        return response()->json($ai,201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Ai $ai)
    {
      $ai->load('instructions');
       return response()->json($ai);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Ai $ai)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
public function update(Request $request, Ai $ai)
{
    $ai->update($request->all());
    return response()->json($ai, 200);
}


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Ai $ai)
    {
        //
    }

public function getAiAnswer2(Request $request)
{
    $question = $request->input('question');
    $instruction_id = $request->input('instruction_id');

    $instruction = ArtificialIntelligenceTraining::where('ai_id', $instruction_id)
                    ->pluck('description')
                    ->first();

    $response = Http::withHeaders([
        'Authorization' => 'Bearer 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',
        'Content-Type'  => 'application/json',
    ])->post('https://openrouter.ai/api/v1/chat/completions', [
        'model' => 'togethercomputer/llama-3-8b-instruct:free', // better at instruction-following
        'messages' => [
            [
                'role' => 'system',
                'content' => $instruction ?? '',
            ],
            [
                'role' => 'user',
                'content' => $question,
            ]
        ]
    ]);

    if (!$response->successful()) {
        return response()->json([
            'error' => 'Failed to get a response from OpenRouter.',
            'details' => $response->body()
        ], $response->status());
    }

    $answer = $response->json('choices.0.message.content');

    return response()->json([
        'question' => $question,
        'instructions' => $instruction,
        'answer' => $answer,
    ]);
}

public function getAiAnswer(Request $request)
{
    $question = $request->input('question');
    $instruction_id = $request->input('instruction_id');

    $instructions = ArtificialIntelligenceTraining::where('ai_id', $instruction_id)
                    ->pluck('description')
                    ->toArray();

    $combinedInstructions = implode("\n", $instructions);

    $fullPrompt = $combinedInstructions . "\n\n" . $question;

    $response = Http::withHeaders([
        'Content-Type' => 'application/json',
    ])->post('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=AIzaSyAuGP7TxA6KPTmzSV3IF8WhTfzIKUfygHU', [
        'contents' => [
            [
                'role' => 'user',
                'parts' => [
                    ['text' => $fullPrompt]
                ]
            ]
        ]
    ]);

    if (!$response->successful()) {
        return response()->json([
            'error' => 'Failed to get a response from Gemini.',
            'details' => $response->body()
        ], $response->status());
    }

    $answer = $response->json('candidates.0.content.parts.0.text');

    return response()->json([
        'question' => $question,
        'instructions' => $instructions,
        'answer' => $answer,
    ]);
}


}
