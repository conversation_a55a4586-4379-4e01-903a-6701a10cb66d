<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LabourRequestList extends Model
{
    protected $fillable = [
        'labour_request_type_id',
        'labour_request_id',
        'labour_id',
        'labour_name'
    ];
    
    public function project(){
        return $this->belongsTo(Project::class);
    }
    
      public function labourRequestType(){
        return $this->belongsTo(LabourType::class,'labour_request_type_id');
    }
}
