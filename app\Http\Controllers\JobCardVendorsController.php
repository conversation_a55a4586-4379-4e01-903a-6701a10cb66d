<?php

namespace App\Http\Controllers;

use App\Models\JobCardVendors;
use Illuminate\Http\Request;

class JobCardVendorsController extends Controller
{
    public function index()
    {
        $jobCards = JobCardVendors::with('jobCard','vendor')->get();
        return response()->json($jobCards);
    }


    public function store(Request $request)
    {
        $jobCardVendors = JobCardVendors::create($request->all());
        return response()->json($jobCardVendors, 201);
    }

    public function show($id)
    {
        $jobCardVendors = JobCardVendors::with('jobCard','vendor')->find($id);        
        if (!$jobCardVendors) {
            return response()->json([
                'message' => 'Job Card Vendor not found',
                'success' => false
            ], 404);
        }
        return response()->json($jobCardVendors);
    }

    public function update(Request $request, $id)
    {
        $jobCardVendors = JobCardVendors::find($id);
        if (!$jobCardVendors) {
            return response()->json([
                'message' => 'Job Card Vendor not found',
                'success' => false
            ], 404);
        }
        $jobCardVendors->update($request->all());
        return response()->json($jobCardVendors);
    }

  
    public function destroy($id)
    {
        $jobCardVendors = JobCardVendors::find($id);
        if (!$jobCardVendors) {
            return response()->json([
                'message' => 'Job Card Vendor not found',
                'success' => false
            ], 404);
        }
        $jobCardVendors->delete();
        return response()->json($jobCardVendors);
    }
}
