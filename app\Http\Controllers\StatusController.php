<?php

namespace App\Http\Controllers;

use App\Models\Status;
use Illuminate\Http\Request;

class StatusController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $itemListCategories = Status::all();
        return response()->json($itemListCategories);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $Status = Status::create($request->all());

        return response()->json($Status, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Status $Status)
    {
        return response()->json($Status);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Status $Status)
    {
        // This method is not typically used in API controllers
        // as APIs usually do not have a UI for editing.
        return response()->json($Status);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Status $Status)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $Status->update($request->all());

        return response()->json($Status);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Status $Status)
    {
        $Status->delete();

        return response()->json("Deleted Successfully", 204);
    }
}
