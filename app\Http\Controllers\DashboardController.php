<?php
//08 jully 2025
namespace App\Http\Controllers;
use App\Models\GoodReceivedNote;
use App\Models\GoodsDeliveryNote;
use App\Models\PurchaseOrder;
use App\Models\Requisition;
use App\Models\Machines;
use App\Models\LabourRequest;
use App\Models\JobCard;
use App\Models\GoodIssueNote;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function getSummary(){
        $total_grn = GoodReceivedNote::with('orders')
          ->whereHas('order', function ($query) {
        $query->where('deleted', 0)->where('status_name','approved');
        })->count();
        
        
        $pending_grn = GoodReceivedNote::with('orders')
          ->whereHas('order', function ($query) {
        $query->where('deleted', 0)
        ->where('status_name', 'approved');
        })->where('remarks',null)->count();
        
        
        $total_lpo = PurchaseOrder::
             whereNot('status_name','Inactive')
             ->where('deleted',0)
            ->where('company_id', 6)
            ->count();
            
        $pending_lpo = PurchaseOrder::
            where('status_name','unapproved')
            ->where('deleted',0)
            ->where('company_id', 6)
            ->count();
            
        $jobcard_requisitions = PurchaseOrder::
            where('status_name','requisition')
            ->where('deleted',0)
            ->where('company_id', 6)
            ->count();
            
           $approved_lpo = PurchaseOrder::
            where('status_name','approved')
            ->where('deleted',0)
            ->where('company_id', 6)
            ->count();
            
            
        //          $total_requisition = Requisition::
        //  whereIn('status_id', [5, 6])
        //  ->count();
     
            
        $total_requisition = Requisition::
            whereNot('status_id',1)
            ->count();       
            
        $total_gin = Requisition::
            whereNot('status_id',1)
            ->where('status_id',9)
            ->count();  
        
        $delivered_gin = GoodsDeliveryNote::count();         
        
        $pending_requisition =  Requisition::
             where('status_id',2)
            ->count();        
        $total_jobcards = jobcard::
             count();   
        $completed_jobcards = jobcard::where('status_id',8)
        ->count();
        
            $active_jobcards = jobcard::where('status_id',6)
        ->count();
        
        $lpo = [
             'total'=>$total_lpo,
             'approved'=>$approved_lpo,
             'unapproved' => $pending_lpo
            ];
        
        $grn = [
             'total'=>$total_grn,
             'unapproved' => $pending_grn
            ];
            
        $assets = [
            'total_assets'=>Machines::count(),
            'machines'=>Machines::where('category_id',1)->count(),
            'vehicles'=>Machines::where('category_id',2)->count(),
            'supervision'=>Machines::where('category_id',3)->count(),
            ];   
        
        $labour_request = [
            'total' => LabourRequest::whereNot('status_name','Inactive')->count(),
            'unapproved' => LabourRequest::where('status_name','Unapproved')->count()
            ];    
            
        $material_requisition = [
             'total'=>$total_requisition,
             'pending' => $pending_requisition
            ];
        
        $gin = [
            'total' =>$total_gin,
            'delivered'=>$delivered_gin
            ];    
         
        $jobcard = [
             'total'=>$total_jobcards,
             'active' => $active_jobcards,
             'completed' => $completed_jobcards,
             'requisitions'=> $jobcard_requisitions
            ];
           
        $data = 
            [
             'lpo'=>$lpo,
             'grn' =>$grn,
             'assets' => $assets,
             'labour_request'=>$labour_request,
             'material_requisition'=>$material_requisition,
             'gin'=>$gin,
             'jobcard' =>$jobcard
            ];
            
            return response()->json($data);
    }
    
public function getUnapprovedLpo()
{
    $lpos = PurchaseOrder::with('items','approvalCheck','project','vendor')
        ->where('status_name', 'Unapproved')
        ->where('deleted', 0)
        ->where('company_id', 6)
        ->orderBy('id','desc')
        ->get();

    $approvalFlow = [
        'operation_department' => 'Operations',
        'engineer' => 'Engineer',
        'senior_accountant' => 'Sr .Accountant',
        'managing_director' => 'MD'
    ];

    $formatted = $lpos->map(function ($lpo) use ($approvalFlow) {
        $total = $lpo->items->sum(function ($item) {
            return (float)$item->quantity * (float)$item->rate;
        });

        $approval = $lpo->approvalCheck->first();
        $awaits = null;

        if ($approval) {
            $passed = false;
            foreach ($approvalFlow as $key => $title) {
                if ($approval->$key == 1) {
                    $passed = true;
                } elseif ($approval->$key == 0 && $passed) {
                    $awaits = $title;
                    break;
                }
            }
        }

        return [
            'id' => $lpo->id,
            'project'=>$lpo->project->short_name??'',
            'supplier'=>$lpo->vendor->name??'',
            'payment_terms' => $lpo->payment_terms,
            'date_created' => $lpo->date_created,
            'requested_by' => $lpo->requested_by,
            'total_amount' => $total,
            'status_name' => $lpo->status_name,
            'status_color' => $lpo->status_color,
            'awaits' => $awaits,
            'approval_id' => $approval->id ?? null

        ];
    });

    return response()->json($formatted);
}

public function getLpo(Request $request)
{
    $statusName = $request->status_name;

    $lpos = PurchaseOrder::with('items', 'approvalCheck', 'project', 'vendor')
        ->when($statusName, function ($query) use ($statusName) {
            $query->where('status_name', $statusName);
        }, function ($query) {
            $query->whereNot('status_name', 'Inactive');
        })
        ->where('deleted', 0)
        ->where('company_id', 6)
        ->orderBy('id', 'desc')
        ->get();

    $approvalFlow = [
        'operation_department' => 'Operations',
        'engineer' => 'Engineer',
        'senior_accountant' => 'Sr .Accountant',
        'managing_director' => 'MD'
    ];

    $formatted = $lpos->map(function ($lpo) use ($approvalFlow) {
        $total = $lpo->items->sum(function ($item) {
            return (float)$item->quantity * (float)$item->rate;
        });

       $approval = $lpo->approvalCheck->first();
$awaits = 'No one';

if ($approval) {
    foreach ($approvalFlow as $key => $title) {
        if ($approval->$key == 0) {
            $awaits = $title;
            break;
        }
    }
}

        return [
            'id' => $lpo->id,
            'project' => $lpo->project->short_name ?? '',
            'supplier' => $lpo->vendor->name ?? '',
            'payment_terms' => $lpo->payment_terms,
            'date_created' => $lpo->date_created,
            'requested_by' => $lpo->requested_by,
            'total_amount' => $total,
            'status_name' => $lpo->status_name,
            'status_color' => $lpo->status_color,
            'awaits' => $awaits ,
            'approval_id' => $approval->id ?? null
        ];
    });

    return response()->json($formatted);
}


public function getUncheckedGrn()
{
    $grns = GoodReceivedNote::with('order.project')
        ->where('remarks', null)
        ->where('deleted', 0)
        ->whereHas('order', function ($query) {
            $query
             ->where('company_id', 6)
             ->where('deleted', 0)
            ->where('status_name', 'Approved');
        })
        ->get();

    // Format each GRN with selected fields
    $formatted = $grns->map(function ($grn) {
        return [
            'id' => $grn->id,
            'order_id' => $grn->order_id,
            'project' => optional($grn->order->project)->short_name,
            'date_created' => optional($grn->order)->date_created,
            'payment_terms' => optional($grn->order)->payment_terms,
        ];
    });

    return response()->json($formatted);
}

public function getJobcards(Request $request)
{
    $jobcards = jobcard::when($request->has('status_id'), function ($query) use ($request) {
        $query->where('status_id', $request->status_id);
    })
    ->with('machine','status')
    ->get();

    return response()->json($jobcards);
}

public function getRequisitions(Request $request)
{
    $query = Requisition::with('requester', 'status', 'type.items')
        ->whereNot('status_id', 1);

    if ($request->status_id) {
        $query->where('status_id', $request->status_id);
    }

    $requisitions = $query->orderBy('id', 'desc')
        ->get()
        ->map(function ($req) {
            return [
                'id' => $req->id,
                'requester' => $req->requester->name ?? null,
                'status' => $req->status->name ?? null,
                'status_color' => $req->status->color ?? null,
                'date' => $req->date,
                'required_date' => $req->required_date,
                'type' => $req->type->name ?? null,
                'items' => $req->type && $req->type->items ? $req->type->items->map(function ($item) {
                    return [
                        'item_id' => $item->item_id,
                        'quantity' => $item->quantity,
                        'rate' => $item->rate,
                        'remark' => $item->remark,
                    ];
                }) : [],
            ];
        });

    return response()->json($requisitions);
}



public function getGin(Request $request)
{
    $query = GoodIssueNote::with(['issuer', 'items.item.item', 'requisition.status']);

    if ($request->has('status_id')) {
        $query->whereHas('requisition', function ($q) use ($request) {
            $q->where('status_id', $request->status_id);
        });
    }

    $notes = $query->get()->map(function ($note) {
        return [
            'id' => $note->id,
            'requisition_id' => $note->requisition_id,
            'date_issued' => $note->date_issued ?? $note->created_at,
            'issuer' => [
                'id' => $note->issuer->id,
                'name' => $note->issuer->name,
                'email' => $note->issuer->email,
            ],
            'status' => $note->requisition->status ?? null,
            'items' => $note->items->map(function ($issueItem) {
                $requisitionItem = $issueItem->item;
                $inventoryItem = $requisitionItem->item;

                return [
                    'item_id' => $inventoryItem->id ?? null,
                    'item_name' => $inventoryItem->name ?? null,
                    'unit' => $inventoryItem->unit ?? null,
                    'remark' => $requisitionItem->remark ?? null,
                    'requested' => $requisitionItem->quantity ?? null,
                    'supplied' => $issueItem->quantity,
                ];
            }),
        ];
    });

    return response()->json($notes);
}





}
