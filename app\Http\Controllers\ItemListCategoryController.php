<?php

namespace App\Http\Controllers;

use App\Models\ItemListCategory;
use Illuminate\Http\Request;

class ItemListCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $itemListCategories = ItemListCategory::all();
        return response()->json($itemListCategories);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $itemListCategory = ItemListCategory::create($request->all());

        return response()->json($itemListCategory, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ItemListCategory $itemListCategory)
    {
        return response()->json($itemListCategory);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ItemListCategory $itemListCategory)
    {
        // This method is not typically used in API controllers
        // as APIs usually do not have a UI for editing.
        return response()->json($itemListCategory);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ItemListCategory $itemListCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $itemListCategory->update($request->all());

        return response()->json($itemListCategory);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ItemListCategory $itemListCategory)
    {
        $itemListCategory->delete();

        return response()->json("Deleted Successfully", 204);
    }
}
