<?php

namespace App\Http\Controllers;

use App\Models\JobCardCostCenter;
use Illuminate\Http\Request;

class JobCardCostCenterController extends Controller
{
    
    public function index()
    {
        $jobCards = JobCardCostCenter::with('jobCard','CostCenter')->get();
        return response()->json($jobCards);
    }

    public function store(Request $request)
    {
        $jobCard = JobCardCostCenter::create($request->all());
        return response()->json($jobCard, 201);
    }


    public function show($id)
    {
        $jobCardCostCenter = JobCardCostCenter::with('jobCard','CostCenter')->find($id);

        if (!$jobCardCostCenter) {
            return response()->json([
                'message' => 'Store not found',
                'success' => false
            ], 404);
        }

        return response()->json( $jobCardCostCenter);
    }


    public function update(Request $request, $id)
    {
        $jobCardCostCenter = JobCardCostCenter::find($id);

        if (!$jobCardCostCenter) {
            return response()->json([
                'message' => 'Store not found',
                'success' => false
            ], 404);
        }

        $jobCardCostCenter->update($request->all());

        return response()->json($jobCardCostCenter);
    }


    public function destroy($id)
    {
        $jobCardCostCenter = JobCardCostCenter::find($id);
         if (!$jobCardCostCenter) {
            return response()->json([
                'message' => 'Store not found',
                'success' => false
            ], 404);
        }    
        
         $jobCardCostCenter->delete(); 

        return response()->json($jobCardCostCenter);
    }
}

