<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BudgetItem extends Model
{
    //

    protected $table = 'budget_items'; // Specify the table name if it's different from the default 'budget_items'

    public function descriptions()
    {
        return $this->hasMany(BudgetItemDescription::class, 'budget_item_id');
    }
    public function budget()
    {
        return $this->belongsTo(Budget::class);
    }
    public function project()
    {
        return $this->belongsTo(Project::class);
    }
}
