<?php

namespace App\Http\Controllers;

use App\Models\Company;
use Illuminate\Http\Request;

class CompanyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json(Company::all());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $Company = Company::create($request->all());

        return response()->json($Company, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Company $Company)
    {
        return response()->json($Company);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Company $Company)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Company $Company)
    {
        $Company->update($request->all());

        return response()->json($Company);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Company $Company)
    {
        $Company->delete();

        return response()->json(['message' => 'Company deleted']);
    }
}
