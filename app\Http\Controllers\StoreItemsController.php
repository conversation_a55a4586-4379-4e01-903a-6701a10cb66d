<?php

namespace App\Http\Controllers;

use App\Models\StoreItems;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class StoreItemsController extends BaseController
{
    protected string $modelClass = StoreItems::class;

    protected function validateRequest(Request $request, ?Model $model = null): array
    {
        // Accept all input without validation
        return $request->all();
    }
}
