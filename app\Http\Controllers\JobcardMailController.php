<?php

namespace App\Http\Controllers;

use App\Models\JobCard;
use App\Models\ItemUsage;
use App\Models\Tyre;
use App\Models\CostNote;

use Illuminate\Http\Request;

class JobCardMailController extends Controller
{
    public function show($id)
    {
        // Fetch the job card with related data
        $jobCard = JobCard::with([
            'costNotes',
            'tyres',
            'itemUsage',
        ])->findOrFail($id);

  

        // Prepare job info
        $jobInfo = [
            'jobNumber' => $jobCard->id,
            'date' => $jobCard->created_at->format('d/m/Y'),
            'serviceType' => $jobCard->service_type,
            'currentKm' => number_format($jobCard->current_km_of_service),
            'nextKm' => number_format($jobCard->next_km_of_service),
            'status' => $jobCard->status_name,
        ];

        $tyresUsed = Tyre::where('jobcard_id', $id)->get()->map(function($tyre, $index) {
            return [
                'serialNumber' => $index + 1,
                'tyreNumber' => $tyre->tyre_no,
                'rate' => number_format($tyre->rate),
                'dateChanged' => $tyre->date_changed->format('d-m-Y'),
            ];
        });

        $sparesRequired = ItemUsage::where('jobcard_id', $id)->get()->map(function($spare, $index) {
            return [
                'serialNumber' => $index + 1,
                'spare' => $spare->item->name,
                'unit' => $spare->item->unit,
                'quantity' => $spare->quantity,
                'rate' => number_format($spare->rate, 2),
                'total' => number_format($spare->quantity * $spare->rate, 2),
                'description' => $spare->description,
            ];
        });

      
        $costNotes = CostNote::where('jobcard_id', $id)->get();
        
        // Prepare labour costs
        $labourCost = $costNotes->where('type', 'labour')->map(function($note, $index) {
            return [
                'serialNumber' => $index + 1,
                'description' => $note->description,
                'quantity' => $note->quantity,
                'rate' => number_format($note->rate, 2),
                'total' => number_format($note->quantity * $note->rate, 2),
            ];
        });

        // Prepare electrical works
        $electricalWorks = $costNotes->where('type', 'electric')->map(function($note, $index) {
            return [
                'serialNumber' => $index + 1,
                'description' => $note->description,
                'quantity' => $note->quantity,
                'rate' => number_format($note->rate, 2),
                'total' => number_format($note->quantity * $note->rate, 2),
            ];
        });

        // Prepare body works (engineering works)
        $engineeringWorks = $costNotes->where('type', 'body_works')->map(function($note, $index) {
            return [
                'serialNumber' => $index + 1,
                'description' => $note->description,
                'quantity' => $note->quantity,
                'rate' => number_format($note->rate, 2),
                'total' => number_format($note->quantity * $note->rate, 2),
            ];
        });

        // Prepare additional works
        $additionalWorks = $costNotes->where('type', 'additional')->map(function($note, $index) {
            return [
                'serialNumber' => $index + 1,
                'description' => $note->description,
                'quantity' => $note->quantity,
                'rate' => number_format($note->rate, 2),
                'total' => number_format($note->quantity * $note->rate, 2),
            ];
        });

        // Prepare engineering works (if you have a separate type for this)
        $engineeringWorks = $costNotes->where('type', 'engineering')->map(function($note, $index) {
            return [
                'serialNumber' => $index + 1,
                'description' => $note->description,
                'quantity' => $note->quantity,
                'rate' => number_format($note->rate, 2),
                'total' => number_format($note->quantity * $note->rate, 2),
            ];
        });


        // Calculate total costs
        $totalCosts = [
            'sparesCost' => number_format($jobCard->itemUsage->sum(function($spare) {
                return $spare->quantity * $spare->rate;
            }), 2),
            'tyresCost' => number_format($jobCard->tyres->sum('rate'), 2),
            'bodyWorksCost' => number_format($costNotes->where('type', 'body_works')->sum(function($work) {
                return $work->quantity * $work->rate;
            }), 2),
            'electricalWorksCost' => number_format($costNotes->where('type', 'electric')->sum(function($work) {
                return $work->quantity * $work->rate;
            }), 2),
            'labourCost' => number_format($costNotes->where('type', 'labour')->sum(function($labour) {
                return $labour->quantity * $labour->rate;
            }), 2),
            'engineeringWorksCost' => number_format($costNotes->where('type', 'engineering')->sum(function($work) {
                return $work->quantity * $work->rate;
            }), 2),
            'additionalWorksCost' => number_format($costNotes->where('type', 'additional')->sum(function($work) {
                return $work->quantity * $work->rate;
            }), 2),
            'totalCost' => number_format($costNotes->sum(function($note) {
                return $note->quantity * $note->rate;
            }) + $jobCard->tyres->sum('rate') + $jobCard->itemUsage->sum(function($spare) {
                return $spare->quantity * $spare->rate;
            }), 2),
        ];

        return view('pdf.report1', [
            'jobCardNumber' => $jobCard->job_number,
            'jobInfo' => $jobInfo,
            'defectsDetected' => $jobCard->defects_detected,
            'workCarriedOut' => $jobCard->work_carried_out,
            'tyresUsed' => $tyresUsed,
            'sparesRequired' => $sparesRequired,
            'engineeringWorks' => $engineeringWorks,
            'labourCost' => $labourCost,
            'electricalWorks' => $electricalWorks,
            'additionalWorks' => $additionalWorks,
            'totalCosts' => $totalCosts,
        ]);
    }
}