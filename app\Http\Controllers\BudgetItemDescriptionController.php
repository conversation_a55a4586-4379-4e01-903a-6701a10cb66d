<?php

namespace App\Http\Controllers;

use App\Models\BudgetItemDescription;
use App\Models\BudgetItem; // Add this import to access the budgetItem relation
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class BudgetItemDescriptionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
public function index()
{
    $budgetItemDescriptions = BudgetItemDescription::with('budgetItem')->get();
    
    // Transform the collection to include the budget_cost
    $transformedItems = $budgetItemDescriptions->map(function ($item) {
        $itemArray = $item->toArray();
        $itemArray['budget_cost'] = $item->cost * $item->quantity * $item->quantity_2;
        return $itemArray;
    });
    
    return response()->json($transformedItems);
}

    
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(BudgetItemDescription $budgetItemDescription)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BudgetItemDescription $budgetItemDescription)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BudgetItemDescription $budgetItemDescription)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BudgetItemDescription $budgetItemDescription)
    {
        //
    }
}