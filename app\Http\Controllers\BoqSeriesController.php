<?php

namespace App\Http\Controllers;

use App\Models\BoqSeries;
use Illuminate\Http\Request;

class BoqSeriesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $BoqSeries = BoqSeries::all();
        return response()->json($BoqSeries);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
       
        $BoqSeries = BoqSeries::create($request->all());
        return response()->json($BoqSeries, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(BoqSeries $BoqSeries)
    {
        return response()->json($BoqSeries);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BoqSeries $BoqSeries)
    {
        // This method is not typically used in API controllers
        // as APIs usually do not have a UI for editing.
        return response()->json($BoqSeries);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BoqSeries $BoqSeries)
    {
        $BoqSeries->update($request->all());

        return response()->json($BoqSeries);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BoqSeries $BoqSeries)
    {
        $BoqSeries->delete();

        return response()->json("Deleted Successfully", 204);
    }
    
       public function getProjectBoq(Request $request){
        $data = BoqSeries::with('sections.subSections.Lines')->where('project_id',$request->project_id)->get();  
        return response()->json(['data'=>$data]);
    }
}
