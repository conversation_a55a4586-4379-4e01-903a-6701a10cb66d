<?php


namespace App\Http\Controllers;

use App\Models\GoodDeliveryNoteItem;
use Illuminate\Http\Request;

class GoodDeliveryNoteItemController extends Controller
{

    public function index()
    {
        $items = GoodDeliveryNoteItem::all();
        return response()->json($items);
    }


    public function store(Request $request)
    {
        $item = GoodDeliveryNoteItem::create($request->all());

        return response()->json(['message' => 'Item created successfully', 'data' => $item], 201);
    }


    public function show($id)
    {
        $item = GoodDeliveryNoteItem::findOrFail($id);
        return response()->json($item);
    }


    public function update(Request $request, $id)
    {
        $item = GoodDeliveryNoteItem::findOrFail($id);
        $item->update($request->all());
        return response()->json(['message' => 'Item updated successfully', 'data' => $item]);
    }

    public function destroy($id)
    {
        $item = GoodDeliveryNoteItem::findOrFail($id);
        $item->delete();

        return response()->json(['message' => 'Item deleted successfully']);
    }
}