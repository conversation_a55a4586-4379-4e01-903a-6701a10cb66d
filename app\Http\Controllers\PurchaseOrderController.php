<?php

namespace App\Http\Controllers;

use App\Models\PurchaseOrder;
use App\Models\ApprovalCheck;
use App\Models\GoodReceivedNote;
use App\Models\Item;
use App\Models\StoreItem;
use App\Models\GrnItem;
use Illuminate\Http\Request;

class PurchaseOrderController extends Controller
{
    // public function index()
    // {
    //     return response()->json(PurchaseOrder::all());
    // }
public function index()
{
    $purchaseOrders = PurchaseOrder::with('project', 'vendor', 'approvalCheck','grn')
        ->orderBy('id', 'desc')
        // ->take(20)
        ->where('deleted',0)
        ->get();
    
    $result = $purchaseOrders->map(function ($order) {
        $approval = $order->approvalCheck->first(); // Get first approval row
        $awaits = "No one";

        if (($approval->operation_department ?? 1) === 0) {
            $awaits = 'Operation';
        } elseif ($order->company_id == 5 && ($approval->project_manager ?? 1) === 0) {
            $awaits = 'P_Manager';
        } elseif ($order->company_id != 5 && ($approval->engineer ?? 1) === 0) {
            $awaits = 'Engineer';
        } elseif (($approval->senior_accountant ?? 1) === 0) {
            $awaits = 'SR.Accountant';
        } elseif (($approval->managing_director ?? 1) === 0) {
            $awaits = 'MD';
        }

        return [
            'id' => $order->id,
            'name'=>$order->name,
            'company_id' => $order->company_id,
            'user_id' => $order->user_id,
            'requested_by' => $order->requested_by,
            'supplier_id' => $order->vendor_id,
            'supplier' => $order->vendor->name ?? null,
            'location' => $order->vendor->location ?? null,
            'date_created' => $order->date_created,
            'payment_terms' => $order->payment_terms,
            'status_name' => $order->status_name,
            'status_color' => $order->status_color,
            'project' => $order->project->project_name ?? null,
            'project_aka' => $order->project->short_name ?? null,
            'project_id' => $order->project->id ?? null,
            'senior_accountant' => $approval->senior_accountant ?? null,
            'awaits' => $awaits,
            'grn_no'=>$order->grn->id ?? 0,
        ];
    });

    return response()->json($result);
}


    public function show($id)
    {
        $order = PurchaseOrder::find($id);

        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        return response()->json($order);
    }

    public function store(Request $request)
    {
        $order = PurchaseOrder::create($request->all());
        ApprovalCheck::create([
            'purchase_order_id' => $order->id,
            'status' => 'incomplete'
        ]);
        
          GoodReceivedNote::create([
            'order_id' => $order->id,
        ]);

        return response()->json($order, 201);
    }

    public function update(Request $request, $id)
    {
        $order = PurchaseOrder::find($id);

        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        $order->update($request->all());

        return response()->json($order);
    }

   public function destroy($id)
{
    $order = PurchaseOrder::find($id);

    if (!$order) {
        return response()->json(['message' => 'Order not found'], 404);
    }

    $order->update(['deleted' => 1]);

    return response()->json(['message' => 'Order deleted']);
}


    public function purchaseOrderCount()
    {
        $orders = PurchaseOrder::count();

        return response()->json(['purchase_order_count' => $orders]);
    }
    
    public function purchaseOrderItems(){
         $items = PurchaseOrder::with('items','company','project.fuelUsage.itemList','project.fuelUsage.machines', 'vendor')->get();
        return response()->json($items);
    }
    
     public function projectPurchaseOrder($project_id)
    {
        $order = PurchaseOrder::where('project_id',$project_id)->get();

        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        return response()->json($order);
    }
    
public function budgetPurchaseOrder(Request $request)
{
    $order = PurchaseOrder::
                  with('vendor')
                ->where('name', $request->budget_name)
                ->get();
    return response()->json($order);
}

public function createStoreLpo(Request $request)
{
    $order = PurchaseOrder::create($request->lpo);

    ApprovalCheck::create([
        'purchase_order_id' => $order->id,
        'status' => 'incomplete',
    ]);

    $grn = GoodReceivedNote::create([
        'order_id' => $order->id,
    ]);

    foreach ($request->items as $item) {
        $lpo_item = Item::create([
            'order_id' => $order->id,
            'description'=>$item['description'], 
            'specification' => $item['name'], 
            'category' => 'store', // adapt based on your schema
            'quantity' => $item['quantity'],
            'unit_measure'=>$item['unit'],
            'rate' => $item['rate'],
        ]);
        
      
        GrnItem::create([
            'grn_id' => $grn->id,
            'asset_id' => $item['id'], 
            'item_id'=> $lpo_item->id
            ]);    
    }

    return response()->json(['message'=>'Order created successfully'], 201);
}


public function deletedOrders()
{
    $purchaseOrders = PurchaseOrder::with('project', 'vendor', 'approvalCheck','grn')
        ->orderBy('id', 'desc')
        // ->take(20)
        ->where('deleted',1)
        ->get();
    
    $result = $purchaseOrders->map(function ($order) {
        $approval = $order->approvalCheck->first(); // Get first approval row
        $awaits = "No one";

        if (($approval->operation_department ?? 1) === 0) {
            $awaits = 'Operation';
        } elseif ($order->company_id == 5 && ($approval->project_manager ?? 1) === 0) {
            $awaits = 'P_Manager';
        } elseif ($order->company_id != 5 && ($approval->engineer ?? 1) === 0) {
            $awaits = 'Engineer';
        } elseif (($approval->senior_accountant ?? 1) === 0) {
            $awaits = 'SR.Accountant';
        } elseif (($approval->managing_director ?? 1) === 0) {
            $awaits = 'MD';
        }

        return [
            'id' => $order->id,
            'name'=>$order->name,
            'company_id' => $order->company_id,
            'user_id' => $order->user_id,
            'requested_by' => $order->requested_by,
            'supplier_id' => $order->vendor_id,
            'supplier' => $order->vendor->name ?? null,
            'location' => $order->vendor->location ?? null,
            'date_created' => $order->date_created,
            'payment_terms' => $order->payment_terms,
            'status_name' => $order->status_name,
            'status_color' => $order->status_color,
            'project' => $order->project->project_name ?? null,
            'project_aka' => $order->project->short_name ?? null,
            'project_id' => $order->project->id ?? null,
            'grn_no'=>$order->grn->id ?? 0,
            'deleted_date'=>$order->deleted_date,
            'deleted_reason'=>$order->deleted_reason,
            'deleted_by'=>$order->deleted_by,
        ];
    });

    return response()->json($result);
}

public function getOrderBySource(Request $request){
    $data= PurchaseOrder::with('vendor','items')->where('source',$request->source)->where('source_id',$request->source_id)->where('deleted',0)->orderBy('id','desc')->get();
    return response()->json($data);
}
}
