<?php

namespace App\Http\Controllers;

use App\Models\GoodIssueNote;
use Illuminate\Http\Request;

class GoodIssueNoteController extends Controller
{

    public function ngugo()
    {
        $itemListCategories = GoodIssueNote::with('issuer','items.item.item')->get();
        return response()->json($itemListCategories);
    }
    
    
public function index(Request $request)
{
    $query = GoodIssueNote::with('issuer', 'items.item.item','requisition.status','deliveryNote')->orderBy('id','desc');

    if ($request->has('requisition_id')) {
        $query->where('requisition_id', $request->requisition_id);
    }

    $notes = $query->get()->map(function ($note) {
        return [
            'id' => $note->id,
            'delivery_note_id'=>$note->deliveryNote-> id ??null,
            'requisition_id' => $note->requisition_id,
            'date_issued' => $note->date_issued,
            'issuer' => [
                'id' => $note->issuer->id,
                'name' => $note->issuer->name,
                'email' => $note->issuer->email,
            ],
            'status'=> $note->requisition->status??null,
         'items' => $note->items->map(function ($issueItem) {
    $requisitionItem = $issueItem->item;
    $inventoryItem = $requisitionItem->item;

    return [
        'gin_item_id' => $issueItem->id, // from good_issue_note_items
        'requisition_item_id' => $requisitionItem->id, // from requisition_items
        'inventory_item_id' => $inventoryItem->id, // from items table
        'item_name' => $inventoryItem->name,
        'unit' => $inventoryItem->unit,
        'remark' => $requisitionItem->remark,
        'requested' => $requisitionItem->quantity,
        'supplied' => $issueItem->quantity,
    ];
}),

        ];
    });

    return response()->json($notes);
}




    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $GoodIssueNote = GoodIssueNote::create($request->all());

        return response()->json($GoodIssueNote, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(GoodIssueNote $GoodIssueNote)
    {
        return response()->json($GoodIssueNote);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(GoodIssueNote $GoodIssueNote)
    {
        // This method is not typically used in API controllers
        // as APIs usually do not have a UI for editing.
        return response()->json($GoodIssueNote);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, GoodIssueNote $GoodIssueNote)
    {

        $GoodIssueNote->update($request->all());

        return response()->json($GoodIssueNote);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(GoodIssueNote $request , $GoodIssueNote)
    {
        $GoodIssueNote->update($request->all());;

        return response()->json("Deleted Successfully", 204);
    }
}
