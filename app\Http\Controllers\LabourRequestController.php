<?php

namespace App\Http\Controllers;

use App\Models\LabourRequest;
use App\Models\ApprovalCheck;
use Illuminate\Http\Request;

class LabourRequestController extends Controller
{
    /**
     * Display a listing of the resource.
     */
// public function index()
// {
//     $requests = LabourRequest::with('project','approvalCheck')->get()->map(function ($item) {
//         return [
//             'id' => $item->id,
//             'project_id' => $item->project_id,
//             'project' => $item->project->short_name ?? null,
//             'date' => $item->date,
//             'days' => $item->days,
//             'task' => $item->task,
//             'status' =>$item->status_name,
//             'status_color' =>$item->status_color,
//             'created_at' => $item->created_at,
//             'updated_at' => $item->updated_at,
//             'awaits' =>$item->ApprovalCheck,
//         ];
//     });

//     return response()->json($requests);
// }

public function index()
{
    $requests = LabourRequest::with('project', 'approvalCheck','user')
        ->orderBy('id', 'desc')
        ->get()
        ->map(function ($item) {
            $approval = $item->approvalCheck;
            $user = $item->user;
            $awaits = 'No one';

            if (($approval->human_resources ?? 1) === 0) {
                $awaits = 'HR';
            } elseif (($approval->operation_department ?? 1) === 0) {
                $awaits = 'Operation';
            } elseif (($approval->engineer ?? 1) === 0) {
                $awaits = 'Engineer';
            } elseif (($approval->senior_accountant ?? 1) === 0) {
                $awaits = 'SR.Accountant';
            } elseif (($approval->managing_director ?? 1) === 0) {
                $awaits = 'MD';
            }

            return [
                'id' => $item->id,
                'user_id' => $item->user_id,
                'project_id' => $item->project_id,
                'project' => $item->project->short_name ?? null,
                'date' => $item->date,
                'days' => $item->days,
                'task' => $item->task,
                'status_name' => $item->status_name,
                'status_color' => $item->status_color,
                'created_at' => $item->created_at,
                'updated_at' => $item->updated_at,
                'approval_check_id' => $approval->id ?? null,
                'requested_by'=>$user->name??null,
                'awaits' => $awaits,
            ];
        });

    return response()->json($requests);
}


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $LabourRequest = LabourRequest::create($request->all());
        ApprovalCheck::create([
            'purchase_order_id' => $LabourRequest->id,
            'type'  => 'labour_request',
            'status' => 'incomplete'
        ]);
        return response()->json($LabourRequest, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(LabourRequest $LabourRequest)
    {
        return response()->json($LabourRequest);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(LabourRequest $LabourRequest)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, LabourRequest $LabourRequest)
    {
        $LabourRequest->update($request->all());

        return response()->json($LabourRequest);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(LabourRequest $LabourRequest)
    {
        $LabourRequest->delete();

        return response()->json(['message' => 'Item list deleted']);
    }
}
