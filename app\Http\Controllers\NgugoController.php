<?php

namespace App\Http\Controllers;

use App\Models\ngugo;
use App\models\project;
use App\models\budget;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class NgugoController extends Controller
{
    public function  getallprojects($projectId): JsonResponse

 {  
    $project = Project::findOrFail($projectId);
        $budgets = Budget::where('project_id', $projectId)->get();

 }
 

}
