<?php

use App\Http\Controllers\AnalysisController;
use App\Http\Controllers\BudgetItemDescriptionController;
use App\Http\Controllers\BudgetItemController;













Route::get('/analysis/{projectId}', [AnalysisController::class, 'getProjectBudgetSummary']);
Route::get('/analysis', [AnalysisController::class, 'getAllProjectsBudgetSummary']);
Route::apiResource('budget-items', BudgetItemController::class);
Route::apiResource('budget-item-descriptions', BudgetItemDescriptionController::class);
