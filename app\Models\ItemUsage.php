<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ItemUsage extends Model
{
    protected $fillable = [
        'usage_type',
        'item_id',
        'item_name',
        'category',
        'category_id',
        'description',
        'quantity',
        'rate',
        'store_id'
    ];
    //  public function spare(){
    //      return $this->belongsTo(ItemList::class,'item_id');
    //  }

     public function store(){
         return $this->belongsTo(Store::class,'category_id');
     }

     public function item(){
         return $this->belongsTo(ItemList::class,'item_id');
     }
     
}
