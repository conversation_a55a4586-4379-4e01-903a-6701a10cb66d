<?php

namespace App\Http\Controllers;

use App\Models\MaintainanceLog;
use Illuminate\Http\Request;

class MaintainanceLogController extends Controller
{
    public function index()
    {
        return response()->json(MaintainanceLog::all());
    }

    public function show($id)
    {
        $log = MaintainanceLog::find($id);

        if (!$log) {
            return response()->json(['message' => 'Log not found'], 404);
        }

        return response()->json($log);
    }

    public function store(Request $request)
    {
        $log = MaintainanceLog::create($request->all());

        return response()->json($log, 201);
    }

    public function update(Request $request, $id)
    {
        $log = MaintainanceLog::find($id);

        if (!$log) {
            return response()->json(['message' => 'Log not found'], 404);
        }

        $log->update($request->all());

        return response()->json($log);
    }

    public function destroy($id)
    {
        $log = MaintainanceLog::find($id);

        if (!$log) {
            return response()->json(['message' => 'Log not found'], 404);
        }

        $log->delete();

        return response()->json(['message' => 'Log deleted']);
    }

    public function getByJobCardId($job_card_id)
    {
        $logs = MaintainanceLog::where('job_card_id', $job_card_id)->get();

        return response()->json($logs);
    }
}
