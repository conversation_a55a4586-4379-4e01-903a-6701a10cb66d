<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WhatsappTemplate extends Model
{
   protected $fillable = [
        'name',       // Unique template identifier (e.g. "alert")
        'message',    // Template text with ((1)), ((2)) placeholders
        'parameters'  // JSON array of {name, description} objects
    ];

    protected $casts = [
        'parameters' => 'array' // Auto-converts between array and JSON
    ];

}
