<?php

namespace App\Http\Controllers;
use App\Models\JobCard; 
use App\Http\Controllers\Controller;

class JobcardRateController extends Controller
{
    public function index()
    {
$jobcards = JobCard::whereHas('tyres', function($q) {
        $q->where('rate', 0);
    })
    ->orWhereHas('itemUsages', function($q) {
        $q->where('rate', 0);
    })
    ->orWhereHas('costNotes', function($q) {
        $q->where('rate', 0);
    })
    ->with(['machine','tyres', 'itemUsages', 'costNotes'])
    ->get();

return response()->json($jobcards);

    }
}
