<?php

namespace App\Http\Controllers;

use App\Models\JobCard;
use App\Models\MaintainanceLog;
use App\Models\ItemUsage;
use App\Models\Tyre;
use App\Models\CostNote;
use Barryvdh\DomPDF\Facade\Pdf;

class Report1 extends Controller
{
    public function generatePDF($id)
    {
        // Fetch the job card with related data
        $jobCard = JobCard::with([
            'costNotes',
            'tyres',
            'itemUsage',
            'maintainanceLogs',
            'machines',
        ])->findOrFail($id);

        // Prepare job info
        $jobInfo = [
            'jobNumber' => '00'.$jobCard->id,
            'vehicle' => $jobCard->machines->type_name . '-' . $jobCard->machines->name,
            'date' => $jobCard->created_at->format('d/m/Y'),
            'serviceType' => $jobCard->service_type,
            'currentKm' => number_format($jobCard->current_km_of_service),
            'nextKm' => number_format($jobCard->next_km_of_service),
            'status' => $jobCard->status_name,
        ];

        $tyresUsed = Tyre::where('jobcard_id', $id)->get()->map(function($tyre, $index) {
            return [
                'serialNumber' => $index + 1,
                'tyreNumber' => $tyre->tyre_no,
                'rate' => number_format($tyre->rate),
                'dateChanged' => $tyre->date_changed->format('d-m-Y'),
            ];
        });

        $logs = MaintainanceLog::where('jobcard_id', $id)->get(); // Changed to job_card_id to match your schema
        
        // Format defects detected as simple numbered list
        $defectsDetected = $logs->where('issue_type', 'defect')
            ->map(function($log, $index) {
                return ($index + 1) . '. ' . $log->description;
            })
            ->implode("\n");

        // Format work carried out as simple numbered list
        $workCarriedOut = $logs->where('issue_type', 'work_done')
            ->map(function($log, $index) {
                return ($index + 0) . '. ' . $log->description;
            })
            ->implode("\n");

        $sparesRequired = ItemUsage::where('jobcard_id', $id)->get()->map(function($spare, $index) {
            return [
                'serialNumber' => $index + 1,
                'spare' => $spare->item->name ?? 'N/A',
                'unit' => $spare->item->unit ?? 'N/A',
                'quantity' => $spare->quantity,
                'rate' => number_format($spare->rate, 2),
                'total' => number_format($spare->quantity * $spare->rate, 2),
                'description' => $spare->description,
            ];
        });

        $costNotes = CostNote::where('jobcard_id', $id)->get();
        
        
        $labourCost = $costNotes->where('type', 'labour')->values()->map(function($note, $index) {
            return [
                'serialNumber' => $index + 1,
                'description' => $note->description,
                'quantity' => $note->quantity,
                'rate' => number_format($note->rate, 2),
                'total' => number_format($note->quantity * $note->rate, 2),
            ];
        });

        $electricalWorks = $costNotes->where('type', 'electric')->values()->map(function($note, $index) {
            return [
                'serialNumber' => $index + 1,
                'description' => $note->description,
                'quantity' => $note->quantity,
                'rate' => number_format($note->rate, 2),
                'total' => number_format($note->quantity * $note->rate, 2),
            ];
        });

        $engineeringWorks = $costNotes->where('type', 'engineering')->values()->map(function($note, $index) {
            return [
                'serialNumber' => $index + 1,
                'description' => $note->description,
                'quantity' => $note->quantity,
                'rate' => number_format($note->rate, 2),
                'total' => number_format($note->quantity * $note->rate, 2),
            ];
        });

        $additionalWorks = $costNotes->where('type', 'additional')->values()->map(function($note, $index) {
            return [
                'serialNumber' => $index + 1,
                'description' => $note->description,
                'quantity' => $note->quantity,
                'rate' => number_format($note->rate, 2),
                'total' => number_format($note->quantity * $note->rate, 2),
            ];
        });

       
        $totalCosts = [
            'sparesCost' => number_format($jobCard->itemUsage->sum(function($spare) {
                return $spare->quantity * $spare->rate;
            }), 2),
            'tyresCost' => number_format($jobCard->tyres->sum('rate'), 2),
            'bodyWorksCost' => number_format($costNotes->where('type', 'body_works')->sum(function($work) {
                return $work->quantity * $work->rate;
            }), 2),
            'electricalWorksCost' => number_format($costNotes->where('type', 'electric')->sum(function($work) {
                return $work->quantity * $work->rate;
            }), 2),
            'labourCost' => number_format($costNotes->where('type', 'labour')->sum(function($labour) {
                return $labour->quantity * $labour->rate;
            }), 2),
            'engineeringWorksCost' => number_format($costNotes->where('type', 'engineering')->sum(function($work) {
                return $work->quantity * $work->rate;
            }), 2),
            'additionalWorksCost' => number_format($costNotes->where('type', 'additional')->sum(function($work) {
                return $work->quantity * $work->rate;
            }), 2),
            'totalCost' => number_format($costNotes->sum(function($note) {
                return $note->quantity * $note->rate;
            }) + $jobCard->tyres->sum('rate') + $jobCard->itemUsage->sum(function($spare) {
                return $spare->quantity * $spare->rate;
            }), 2),
        ];

        $data = [
            'jobCardNumber' => $jobCard->job_number,
            'jobInfo' => $jobInfo,
            'defectsDetected' => $defectsDetected, 
            'workCarriedOut' => $workCarriedOut,   
            'tyresUsed' => $tyresUsed,
            'logs' => $logs,
            'sparesRequired' => $sparesRequired,
            'engineeringWorks' => $engineeringWorks,
            'labourCost' => $labourCost,
            'electricalWorks' => $electricalWorks,
            'additionalWorks' => $additionalWorks,
            'totalCosts' => $totalCosts,
        ];

        $pdf = PDF::loadView('pdf.report1', $data);
        
        return $pdf->stream('jobcard-report.pdf');
    }
}