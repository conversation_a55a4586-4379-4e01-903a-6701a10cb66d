<?php
namespace App\Http\Controllers;

use App\Models\Distribution;
use Illuminate\Http\Request;
use App\Models\Item;


class DistributionController extends Controller
{
    public function index()
    {
        return response()->json(Distribution::orderBy('id', 'desc')->get());
    }

    public function store(Request $request)
    {
        $request->validate([
            'machine' => 'required|string',
            'fuel_quantity' => 'required|numeric',
            'bulk_id' => 'required|numeric',
            'ratio' => 'required|string',
            'date_provided' => 'required|date',
        ]);
    
         $fuel = Item::where('id', $request->bulk_id)->value('quantity');
    
        if ($fuel < $request->fuel_quantity) {
            return response()->json(['message' => 'Not enough fuel in bulk'],400);
        }
    
        $distribution = Distribution::create([
            'machine' => $request->machine,
            'machine_id' => $request->machine_id,
            'issuer'=> $request->issuer,
            'issuer_id'=> $request->issuer_id,
            'fuel_quantity' => $request->fuel_quantity,
            'fuel_issued' => $request->fuel_quantity,
            'bulk_id' => $request->bulk_id,
            'ratio' => $request->ratio,
            'date_provided' => $request->date_provided,
        ]);
    
    
        return response()->json($distribution, 201);
    }

public function show($id)
{
    $distribution = Distribution::with('machines')->findOrFail($id);

    return response()->json([
        'id' => $distribution->id,
        'machine' => $distribution->machines->name ?? null,
        'fuel_issued' => $distribution->fuel_issued,
        'fuel_quantity' => $distribution->fuel_quantity,
        'bulk_id' => $distribution->bulk_id,
        'ratio' => $distribution->ratio,
        'ratio_factor' => $distribution->ratio_factor,
        'issuer' => $distribution->issuer,
        'issuer_id' => $distribution->issuer_id,
        'machine_id' => $distribution->machine_id,
        'date_provided' => $distribution->date_provided,
        'created_at' => $distribution->created_at,
        'updated_at' => $distribution->updated_at,
        'type_name' => $distribution->machines->type_name ?? null,
        'ratio_value' => $distribution->machines->ratio ?? null,
        'capacity' => $distribution->machines->capacity ?? null,
        'reserve_litre' => $distribution->machines->reserve_litre ?? null,
    ]);
}



    // In DistributionController.php
// In DistributionController.php
public function bulkDistribution($bulk_id)
{
    $distributions = Distribution::where('bulk_id', $bulk_id)->get();

    if ($distributions->isEmpty()) {
        return response()->json(['message' => 'No distributions found'], 404);
    }

    return response()->json($distributions);
}

public function machineDistribution($machine_id)
{
    $distributions = Distribution::with('items.purchaseOrder')->where('machine_id', $machine_id)->get();

    if ($distributions->isEmpty()) {
        return response()->json(['message' => 'No machine distribution record found'], 404);
    }

    $flatData = $distributions->map(function ($distribution) {
        return [
            'id' => $distribution->id,
            // 'machine' => $distribution->machine,
            'fuel_issued' => $distribution->fuel_issued,
            // 'fuel_quantity' => $distribution->fuel_quantity,
            // 'bulk_id' => $distribution->bulk_id,
            // 'ratio' => $distribution->ratio,
            // 'ratio_factor' => $distribution->ratio_factor,
            'issuer' => $distribution->issuer,
            // 'issuer_id' => $distribution->issuer_id,
            // 'machine_id' => $distribution->machine_id,
            'date_provided' => $distribution->date_provided,
            // 'created_at' => $distribution->created_at,
            // 'updated_at' => $distribution->updated_at,
            // 'item_id' => $distribution->items->id ?? null,
            // 'order_id' => $distribution->items->order_id ?? null,
            // 'description' => $distribution->items->description ?? null,
            // 'unit_measure' => $distribution->items->unit_measure ?? null,
            'quantity' => $distribution->items->quantity ?? null,
            'rate' => $distribution->items->rate ?? null,
            // 'item_created_at' => $distribution->items->created_at ?? null,
            // 'item_updated_at' => $distribution->items->updated_at ?? null,
            'purchase_order_id' => $distribution->items->purchaseOrder->id ?? null,
            // 'company_id' => $distribution->items->purchaseOrder->company_id ?? null,
            // 'user_id' => $distribution->items->purchaseOrder->user_id ?? null,
            // 'requested_by' => $distribution->items->purchaseOrder->requested_by ?? null,
            // 'supplier' => $distribution->items->purchaseOrder->supplier ?? null,
            // 'date_created' => $distribution->items->purchaseOrder->date_created ?? null,
            // 'address' => $distribution->items->purchaseOrder->address ?? null,
            'project' => $distribution->items->purchaseOrder->project ?? null,
            // 'payment_terms' => $distribution->items->purchaseOrder->payment_terms ?? null,
            // 'location' => $distribution->items->purchaseOrder->location ?? null,
            // 'status_name' => $distribution->items->purchaseOrder->status_name ?? null,
            // 'status_color' => $distribution->items->purchaseOrder->status_color ?? null,
            // 'purchase_order_created_at' => $distribution->items->purchaseOrder->created_at ?? null,
            // 'purchase_order_updated_at' => $distribution->items->purchaseOrder->updated_at ?? null,
        ];
    });

    return response()->json($flatData);
}


    public function update(Request $request, $id)
    {
        $distribution = Distribution::findOrFail($id);
        $distribution->update($request->all());
        return response()->json($distribution);
    }

    public function destroy($id)
    {
        Distribution::findOrFail($id)->delete();
        return response()->json(['message' => 'Deleted successfully']);
    }
    
    public function getDistributionCount(){
        $totalCount = Distribution::count();
       return response()->json(['totalDieselStatement' => $totalCount]);
    } 
}
