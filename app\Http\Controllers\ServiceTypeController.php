<?php

namespace App\Http\Controllers;

use App\Models\ServiceType;
use Illuminate\Http\Request;

class ServiceTypeController extends Controller
{
    public function index()
    {
        return response()->json(ServiceType::all());
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
        ]);

        $serviceType = ServiceType::create($request->all());
        return response()->json($serviceType, 201);
    }

    public function show($id)
    {
        $serviceType = ServiceType::findOrFail($id);
        return response()->json($serviceType);
    }

    public function update(Request $request, $id)
    {
        $serviceType = ServiceType::findOrFail($id);
        $serviceType->update($request->all());
        return response()->json($serviceType);
    }

    public function destroy($id)
    {
        ServiceType::findOrFail($id)->delete();
        return response()->json(['message' => 'Deleted successfully']);
    }
}
