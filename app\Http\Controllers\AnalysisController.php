<?php

namespace App\Http\Controllers;

use App\Models\BudgetItemDescription;
use App\Models\BudgetItem;
use App\Models\Budget;
use App\Models\Project;
use App\Models\Remark;
use Illuminate\Http\JsonResponse;

class AnalysisController extends Controller
{
    /**
     * Get project budget summary
     *
     * @param int $projectId
     * @return JsonResponse
     */
   public function getProjectBudgetSummary($projectId): JsonResponse
    {
        $project = Project::findOrFail($projectId);
        $budgets = Budget::where('project_id', $projectId)->get();
        $totalFundedAmount = $budgets->sum('funded_amount');
        
        $totalBudgetCost = 0;
        $totalUsedAmount = 0;
        
        foreach ($budgets as $budget) {
            $budgetItems = BudgetItem::where('budget_id', $budget->id)->get();
            
            foreach ($budgetItems as $budgetItem) {
                $descriptions = BudgetItemDescription::where('budget_item_id', $budgetItem->id)->get();
                foreach ($descriptions as $desc) {
                    $itemCost = $desc->cost * ($desc->quantity * ($desc->quantity_2 ?? 1));
                    $totalBudgetCost += $itemCost;
                    
                    // Get used amount from remarks for this description
                    $usedAmount = Remark::where('budget_item_description_id', $desc->id)
                        ->sum('cost');
                    $totalUsedAmount += $usedAmount;
                }
            }
        }

        $totalUnfundedAmount = $totalBudgetCost - $totalFundedAmount;
        $response = [
            'project_name' => $project->short_name,
            'budgets_count' => $budgets->count(),
            'project_cost' => $project->project_cost,
            'budget_cost' => $totalBudgetCost,
            'used_amount' => $totalUsedAmount,
            'funded_amount' => $totalFundedAmount,
            'total_unfunded_amount' => $totalUnfundedAmount,
            'budgets' => $budgets->map(function($budget) {
                $budgetCost = 0;
                $usedAmount = 0;
                
                $budgetItems = BudgetItem::where('budget_id', $budget->id)->get();
                foreach ($budgetItems as $budgetItem) {
                    $descriptions = BudgetItemDescription::where('budget_item_id', $budgetItem->id)->get();
                    foreach ($descriptions as $desc) {
                        $budgetCost += $desc->cost * ($desc->quantity * ($desc->quantity_2 ?? 1));
                        $usedAmount += Remark::where('budget_item_description_id', $desc->id)
                            ->sum('cost');
                    }
                }
                
                return [
                    'budget_id' => $budget->id,
                    'budget_name' => $budget->name,
                    'funded_amount' => $budget->funded_amount,
                    'budget_cost' => $budgetCost,
                    'used_amount' => $usedAmount,
                    'remaining_amount' => $budget->funded_amount - $usedAmount
                ];
            })
        ];

        return response()->json($response);
    }

    /**
     * Get budget summary for all projects
     *
     * @return JsonResponse
     */
     public function getAllProjectsBudgetSummary(): JsonResponse
    {
        $projects = Project::all();
        $result = [];

        foreach ($projects as $project) {
            $budgets = Budget::where('project_id', $project->id)->get();
            $totalFundedAmount = $budgets->sum('funded_amount');
            $totalBudgetCost = 0;
            $totalUsedAmount = 0;

            // Calculate budget items cost for each budget
            $budgetsWithCosts = $budgets->map(function($budget) use (&$totalBudgetCost, &$totalUsedAmount) {
                $budgetCost = 0;
                $budgetUsedAmount = 0;
                $budgetItems = BudgetItem::where('budget_id', $budget->id)->get();
                
                foreach ($budgetItems as $budgetItem) {
                    $descriptions = BudgetItemDescription::where('budget_item_id', $budgetItem->id)->get();
                    
                    foreach ($descriptions as $desc) {
                        $itemCost = $desc->cost * ($desc->quantity * ($desc->quantity_2 ?? 1));
                        $budgetCost += $itemCost;
                        
                        // Get used amount from remarks for this description
                        $usedAmount = Remark::where('budget_item_description_id', $desc->id)
                            ->sum('cost');
                        $budgetUsedAmount += $usedAmount;
                    }
                }
                
                $totalBudgetCost += $budgetCost;
                $totalUsedAmount += $budgetUsedAmount;
                $unfundedAmount = $budgetCost - $budget->funded_amount;
                
                return [
                    'budget_id' => $budget->id,
                    'budget_name' => $budget->name,
                    'budget_status' => $budget->status,
                    'funded_amount' => $budget->funded_amount,
                    'budget_cost' => $budgetCost,
                    'used_amount' => $budgetUsedAmount,
                    'remaining_amount' => $budget->funded_amount - $budgetUsedAmount,
                    'unfunded_amount' => $unfundedAmount
                ];
            });

            $totalUnfundedAmount = $totalBudgetCost - $totalFundedAmount;

            $result[] = [
                'project_id' => $project->id,
                'project_name' => $project->short_name,
                'budgets_count' => $budgets->count(),
                'project_cost' => $project->project_cost,
                'total_budget_cost' => $totalBudgetCost,
                'total_used_amount' => $totalUsedAmount,
                'total_funded_amount' => $totalFundedAmount,
                'total_unfunded_amount' => $totalUnfundedAmount,
                'total_remaining_amount' => $totalFundedAmount - $totalUsedAmount,
                'budgets' => $budgetsWithCosts
            ];
        }

        return response()->json($result);
    }
}