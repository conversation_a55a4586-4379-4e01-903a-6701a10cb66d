<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DailyWorkLog extends Model
{
        protected $fillable = [
        'request_id',
        'labour_id',
        'work_date',
        'start_hour',
        'end_hour',
        'machine_id',
        'machine',
        'fuel_used',
        'activity',
        'target',
        'remark',
        'target_description',
        'advance',
        'md_remark'
    ];
    
    public function labour(){
    return $this->belongsTo(Labour::class);
}

}

