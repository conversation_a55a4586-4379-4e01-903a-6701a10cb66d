<?php

namespace App\Http\Controllers;

use App\Models\ItemList;
use Illuminate\Http\Request;

class ItemListController extends Controller
{
    /**
     * Display a listing of the resource.
     */
 public function index(Request $request)
{
    $query = ItemList::with('category')->orderBy('id', 'desc');

    if ($request->has('category_id')) {
        $query->where('category_id', $request->category_id);
    }

    $data = $query->get();
    return response()->json($data);
}


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $itemList = ItemList::create($request->all());

        return response()->json($itemList, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ItemList $itemList)
    {
        return response()->json($itemList);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ItemList $itemList)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ItemList $itemList)
    {
        $itemList->update($request->all());

        return response()->json($itemList);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ItemList $itemList)
    {
        $itemList->delete();

        return response()->json(['message' => 'Item list deleted']);
    }
    
  public function getMaterials(Request $request)
{
    if ($request->has('category_id')) {
        $data = ItemList::where('category_id', $request->category_id)->get();
    } else {
        $data = ItemList::whereIn('category_id', [5,6])->orderBy('name','asc')->get();
    }

    return response()->json($data);
}
}
