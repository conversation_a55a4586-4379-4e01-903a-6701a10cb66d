<?php

namespace App\Http\Controllers;

use App\Models\ApprovalGroup;
use App\Models\ApprovalRole;
use Illuminate\Http\Request;

class ApprovalGroupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $groups = ApprovalGroup::with('approvalRoles.role')->get();
        $data = $groups->map(function($group){
          return [
            'group'=>$group->name,
            'roles'=>$group->approvalRoles->map(function ($roleGroup){
                return [
                   'name'=>$roleGroup->role->name,
                   'position'=>$roleGroup->position
                ];
            })
          ];
        });
        return response()->json($data);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }


//  public function store(Request $request)
//     {
//         return response()->json($request, 201);
//     }



public function store(Request $request)
{
    $ApprovalGroup = ApprovalGroup::create($request->all());

    foreach ($request->roles as $role) {
        ApprovalRole::create([
            'approval_group_id' => $ApprovalGroup->id,
            'role_id' => $role['role_id'],
            'position' => $role['position'],
        ]);
    }

    return response()->json(["message"=>"group created successfully"], 201);
}


    /**
     * Display the specified resource.
     */
public function show(ApprovalGroup $ApprovalGroup)
{
    $ApprovalGroup->load('approvalRoles.role');

    $data = $ApprovalGroup->approvalRoles->map(function ($approvalRole) use ($ApprovalGroup) {
        return [
            'group' => $ApprovalGroup->name,
            'role' => $approvalRole->role->name ?? null,
            'position' => $approvalRole->position,
        ];
    });

    return response()->json($data);
}


    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ApprovalGroup $ApprovalGroup)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ApprovalGroup $ApprovalGroup)
    {
        $ApprovalGroup->update($request->all());

        return response()->json($ApprovalGroup);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ApprovalGroup $ApprovalGroup)
    {
        $ApprovalGroup->delete();

        return response()->json(['message' => 'ApprovalGroup deleted']);
    }

 

}
