<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GoodIssueNote extends Model
{
    protected $fillable = [
        'requisition_id',
        'issuer_id',
        'date_issued'
    ];
    
    public function issuer(){
        return $this->belongsTo(User::class,'issuer_id');
    }
    
      public function requisition(){
        return $this->belongsTo(Requisition::class,'requisition_id');
    }
    
    public function items (){
        return $this->hasMany(GoodIssueNoteItem::class,);
    }
    
    public function deliveryNote(){
        return $this->hasOne(GoodsDeliveryNote::class,'gin_id');
    }
}
