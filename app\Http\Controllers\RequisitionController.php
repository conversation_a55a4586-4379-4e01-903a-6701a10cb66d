<?php

namespace App\Http\Controllers;

use App\Models\Requisition;
use App\Models\RequisitionType;
use App\Models\RequisitionTypeItem;
use Illuminate\Http\Request;

class RequisitionController extends Controller
{
    /**
     * Display a listing of the resource.
     */



public function index()
{
    $requisitions = Requisition::with('requester','status','type.items')
        ->orderBy('id','desc')
        ->get()
        ->map(function ($req) {
            return [
                'id' => $req->id,
                'attachment'=>$req->attachment,
                'requester' => $req->requester->name ?? null,
                'status' => $req->status->name ?? null,
                'status_color' => $req->status->color ?? null,
                'date' => $req->date,
                'required_date' => $req->required_date,
                'type' => $req->type->name ?? null,
                'items' => $req->type && $req->type->items ? $req->type->items->map(function ($item) {
                    return [
                        'item_id' => $item->item_id,
                        'quantity' => $item->quantity,
                        'rate' => $item->rate,
                        'remark' => $item->remark,
                    ];
                }) : [],
            ];
        });

    return response()->json($requisitions);
}



    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
 
        $Requisition = Requisition::create($request->all());

        return response()->json($Requisition, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Requisition $Requisition)
    {
        return response()->json($Requisition);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Requisition $Requisition)
    {
        // This method is not typically used in API controllers
        // as APIs usually do not have a UI for editing.
        return response()->json($Requisition);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Requisition $Requisition)
    {

        $Requisition->update($request->all());

        return response()->json($Requisition);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Requisition $Requisition)
    {
        $Requisition->delete();

        return response()->json("Deleted Successfully", 204);
    }
    


public function createRequisition(Request $request)
{
    $requisitionData = $request->input('requisition');
    if (is_string($requisitionData)) {
        $requisitionData = json_decode($requisitionData, true);
    }

   if ($request->hasFile('image')) {
    $file = $request->file('image');
    $filename = time() . '_' . $file->getClientOriginalName();
    $file->move(public_path('proforma'), $filename);
    $requisitionData['attachment'] = 'proforma/' . $filename;
}


    $requisition = Requisition::create($requisitionData);

    $typeData = $request->input('type');
    if (is_string($typeData)) {
        $typeData = json_decode($typeData, true);
    }

    $type = RequisitionType::create([
        "requisition_id" => $requisition->id,
        "name"           => $typeData['name'],
        "item_id"        => $typeData['item_id'],
    ]);

    $items = $request->input('items');
    if (is_string($items)) {
        $items = json_decode($items, true);
    }

    foreach ($items as $item) {
        RequisitionTypeItem::create([
            'requisition_type_id' => $type->id,
            'item_id'             => $item['item_id'],
            'quantity'            => $item['quantity'],
            'rate'                => $item['rate'],
            'remark'              => $item['remark'],
        ]);
    }

    return response()->json([
        'message'     => 'Requisition created successfully',
        'requisition' => $requisition,
        'type'        => $type,
        'items'       => $items
    ], 201);
}



}
