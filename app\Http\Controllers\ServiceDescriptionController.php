<?php

namespace App\Http\Controllers;

use App\Models\ServiceDescription;
use Illuminate\Http\Request;

class ServiceDescriptionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $serviceDescriptions = ServiceDescription::all();
        return response()->json($serviceDescriptions);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $serviceDescription = ServiceDescription::create($request->all());

        return response()->json($serviceDescription, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ServiceDescription $serviceDescription)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ServiceDescription $serviceDescription)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ServiceDescription $serviceDescription)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ServiceDescription $serviceDescription)
    {
        //
    }
}
