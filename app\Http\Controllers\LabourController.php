<?php

namespace App\Http\Controllers;

use App\Models\Labour;
use Carbon\Carbon;
use Illuminate\Http\Request;

class LabourController extends Controller
{
    /**
     * Display a listing of the resource.
     */
public function index()
{
    // $labours = Labour::with('labourRequestList')->get();
        // $labours = Labour::with('labourRequestList.labourRequestType')->get();
        $labours = Labour::all();
    return response()->json($labours);
}
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        Labour::create($request->all());
        return response()->json(['message' => 'Labour created successfully'], 201);
    }

    /**
     * Display the specified resource.
     */
public function show(Labour $labour)
{
    return response()->json($labour);
}


    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Labour $labour)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Labour $labour)
    {
        $labour->update($request->all());
        return response()->json([$labour,'message' => 'Labour updated successfully'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
  public function destroy(Labour $labour)
{
    if ($labour->dailyWorkLog()->exists()) {
        return response()->json(['message' => 'Cannot delete: Labour has related daily work logs.'], 400);
    }

    $labour->delete();
    return response()->json(['message' => 'Labour deleted successfully.']);
}

    
        public function getWeeklyData()
    {
        return response()->json(Labour::
            with('dailyWorkLog')->get());
    }
    
public function approvedLabours(Request $request)
{
    $inputDate = $request->input('date', now()->toDateString());
    $requestId = $request->input('request_id');

    $tuesday = Carbon::parse($inputDate)->startOfWeek(Carbon::TUESDAY);
    $monday = $tuesday->copy()->addDays(6);

    $labours = Labour::whereHas('labourRequestList', function ($query) use ($requestId) {
        if ($requestId) {
            $query->where('labour_request_id', $requestId);
        }
    })
    ->with([
        'dailyWorkLog' => function($query) use ($tuesday, $monday) {
            $query->whereBetween('work_date', [$tuesday, $monday]);
        },
        'labourRequestList' => function ($query) use ($requestId) {
            if ($requestId) {
                $query->where('labour_request_id', $requestId);
            }
        },
        'labourRequestList.labourRequestType'
    ])
    ->get();

    $dayNames = ['Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday', 'Monday'];
    $daysRange = collect();
    foreach ($dayNames as $i => $dayName) {
        $date = $tuesday->copy()->addDays($i)->toDateString();
        $daysRange->put($dayName, $date);
    }

    $labours->transform(function($labour) use ($daysRange) {
        $totalAdvance = 0;

        $logs = $daysRange->map(function($date, $dayName) use ($labour, &$totalAdvance) {
            $log = $labour->dailyWorkLog->firstWhere('work_date', $date);
            if (!$log) return ['hours' => '---', 'target' => null, 'advance' => null];

            $start = $log->start_hour??0;
            $end = $log->end_hour??0;
            $diff = $end-$start;

            if ($log->advance) {
                $totalAdvance += $log->advance;
            }

            return [
                'hours' => $diff,
                'target' => $log->target,
                'advance' => $log->advance,
            ];
        });

        return [
            'id' => $labour->id,
            'project_id' => $labour->project_id ?? null,
            'project_name' => $labour->project_name ?? null,
            'name' => $labour->name,
            'rate' => optional($labour->labourRequestList->first()?->labourRequestType)->rate??0,
            'designation' => $labour->designation,
            'weekly_work' => $logs,
            'total_advance' => $totalAdvance,
        ];
    });

    return response()->json($labours);
}



}
