<?php

namespace App\Http\Controllers;

use App\Models\ApprovalCheck;
use App\Models\PurchaseOrder;
use Illuminate\Http\Request;

class ApprovalCheckController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json(ApprovalCheck::all());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $approvalCheck = ApprovalCheck::create($request->all());

        return response()->json($approvalCheck, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ApprovalCheck $approvalCheck)
    {
        return response()->json($approvalCheck);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ApprovalCheck $approvalCheck)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ApprovalCheck $approvalCheck)
    {
        $approvalCheck->update($request->all());

        if ($request->has('managing_director')) {
            $purchaseOrder = PurchaseOrder::find($approvalCheck->purchase_order_id);
            if ($purchaseOrder) {
            $purchaseOrder->status_color = 'success';
            $purchaseOrder->status_name = 'approved';
            $purchaseOrder->save();
            }
        }

        return response()->json($approvalCheck);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ApprovalCheck $approvalCheck)
    {
        $approvalCheck->delete();

        return response()->json(['message' => 'ApprovalCheck deleted']);
    }

    /**
     * Fetch approval checks by order_id.
     */
    // public function getByOrderId($orderId)
    // {
    //     $approvalChecks = ApprovalCheck::where('purchase_order_id', $orderId)->first();

    //     return response()->json($approvalChecks);
    // }
    
   public function getByOrderId(Request $request, $orderId)
{
    $type = $request->query('type', 'lpo');

    $approvalChecks = ApprovalCheck::where('purchase_order_id', $orderId)
        ->where('type', $type)
        ->first();

    return response()->json($approvalChecks);
}



    
public function getByWagesId(Request $request, $wagesId)
{
    // Force type="wages" and fetch only by wages_id
    $approvalCheck = ApprovalCheck::where('purchase_order_id', $wagesId)
                                 ->where('type', 'wages') // Hardcoded to 'wages'
                                 ->first();

    if (!$approvalCheck) {
        return response()->json(['error' => 'No wages record found'], 404);
    }

    return response()->json($approvalCheck);
}

public function rejectOrder(Request $request){
    ApprovalCheck::where('purchase_order_id', $request->order_id)->update([
        'operation_department' => 0,
        'engineer' => 0,
        'senior_accountant' => 0,
        'managing_director' => 0,
    ]);

    return response()->json(['message' => 'Order Rejected']);
}


}
