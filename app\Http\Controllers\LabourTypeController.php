<?php

namespace App\Http\Controllers;

use App\Models\LabourType;
use Illuminate\Http\Request;

class LabourTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json(LabourType::all());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $labourType = LabourType::create($request->all());

        return response()->json($labourType, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(LabourType $labourType)
    {
        return response()->json($labourType);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(LabourType $labourType)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, LabourType $labourType)
    {
        $labourType->update($request->all());

        return response()->json($labourType);
    }

    /**
     * Remove the specified resource from storage.
     */
  public function destroy($id)
    {
        $labourType = LabourType::find($id);

        if (!$labourType) {
            return response()->json(['message' => 'LabourType not found '], 404);
        }

        $labourType->delete();

        return response()->json(['message' => 'LabourType deleted ']);
    }

}
