<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    protected $fillable = [
        'name',
        'description',
        'path'
    ];
    
        public function rolePermissions()
    {
        return $this->hasMany(RolePermission::class, 'role_id');
    }
    
    
    // In Role model
public function permissions()
{
    return $this->belongsToMany(Permission::class, 'role_permission', 'role_id', 'permission_id');
}
}
