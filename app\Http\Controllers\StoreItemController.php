<?php

namespace App\Http\Controllers;

use App\Models\StoreItem;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class StoreItemController extends Controller
{
    public function index()
    {
      $items = StoreItem::with('spares')->get();
         return response()->json([
            'data' => $items,
            'message' => 'Items retrieved successfully',
            'success' => true
        ]);
    }

    public function show($id)
{
    $item = StoreItem::find($id);

    if (!$item) {
        return response()->json([
            'message' => 'Item not found',
            'success' => false
        ], 404);
    }

    return response()->json([
        'data' => $item,
        'message' => 'Item retrieved successfully',
        'success' => true
    ]);
}

public function store(Request $request)
{
    $existing = StoreItem::where('item_id', $request->item_id)->first();

    if ($existing) {
        $existing->quantity += $request->quantity;
        $existing->save();
        $item = $existing;
    } else {
        $item = StoreItem::create($request->all());
    }

    return response()->json([
        'data' => $item,
        'message' => 'Item created successfully',
        'success' => true
    ]);
}


    public function update(Request $request, $id)
    {
        $storeItem = StoreItem::find($id);

        if (!$storeItem) {
            return response()->json([
                'message' => 'Item not found',
                'success' => false
            ], 404);
        }

        $storeItem->update([
            'spare_id' => $request->spare_id,
            'quantity' => $request->quantity,
            'order_item_id' => $request->order_item_id,
        ]);
    
        return response()->json([
            'data' => $storeItem,
            'message' => 'Item updated successfully',
            'success' => true
        ]);
    }

    public function destroy($id)
    {
        $item = StoreItem::find($id);

        if (!$item) {
            return response()->json([
                'message' => 'Item not found',
                'success' => false
            ], 404);
        }

        $item->delete();

        return response()->json([
            'data' => $item,
            'message' => 'Item deleted successfully',
            'success' => true
        ]);
    }
    
    public function updateItem(array $itemData)
{
    $existing = StoreItem::where('item_id', $itemData['item_id'])->first();

    if ($existing) {
        $existing->quantity += $itemData['quantity'];
        $existing->save();
        return $existing;
    } else {
        return StoreItem::create($itemData);
    }
}

}
