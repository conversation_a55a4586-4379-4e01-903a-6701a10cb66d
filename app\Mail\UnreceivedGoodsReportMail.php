<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class UnreceivedGoodsReportMail extends Mailable
{
    use Queueable, SerializesModels;

    public function build()
    {
        return $this->from('<EMAIL>', 'COPEX CONTRACTORS CO.LTD')
                   ->subject('Unreceived Goods Report - ' . now()->format('d/m/Y'))
                   ->view('emails.unreceived_goods_report'); // Blade template
    }
    public function development(){
        return view('emails.unreceived_goods_report'); 
    }
}