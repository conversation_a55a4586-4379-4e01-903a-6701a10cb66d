<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Passenger extends Model
{
    protected $fillable = [
        'whatsapp_id',
        'name',
        'phone',
        'source_id',
        'source',
        'destination_id',
        'destination',
        'sub_route_id',
        'schedule_id',
        'departure_date',
        'bus_class',
        'fare',
        'seat',
        
    ];

    public function source()
    {
        return $this->belongsTo(Location::class, 'source_id');
    }

    public function destination()
    {
        return $this->belongsTo(Location::class, 'destination_id');
    }

    public function schedule()
    {
        return $this->belongsTo(Schedule::class, 'schedule_id');
    }
}
