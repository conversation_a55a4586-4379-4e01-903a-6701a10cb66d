<?php

namespace App\Http\Controllers;

use App\Models\MachineFuelUsage;
use App\Models\Distribution;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class MachineFuelUsageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
   public function index()
{
    //   $usage = MachineFuelUsage::with('machines', 'bulk.purchaseOrder')->get();
    $usage = MachineFuelUsage::with('machines','project')
    ->orderBy('id','desc')
    ->get();

    $formattedUsage = $usage->map(function ($statement, $index) {
        return [
            'id'=>$statement->id,
            'item_id'=>$statement->item_id,
            'fuel_quantity' => $statement->fuel_quantity,
            'ratio_factor' => $statement->ratio_factor,
            'usage_date' => $statement->usage_date,
            'task' => $statement->task,
            'driver_name' => $statement->driver_name,
            'supervisor_name' => $statement->supervisor_name,
            'initial_reading' => $statement->initial_reading,
            'final_reading' => $statement->final_reading,
            'machine' => $statement->machines->name ?? null,
            'type' => $statement->machines->type_name ?? null,
            'project_id' => $statement->project_id ?? null,
            'company_id'=>$statement->project->company_id,
        //   'project_id' => $statement->bulk->purchaseOrder->project_id ?? null,
        ];
    });

    return response()->json($formattedUsage);
}


    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        // Validation rules
        $validator = Validator::make($request->all(), [
            'machine_id' => 'required|integer',
            'fuel_quantity' => 'required|numeric',
            'ratio_factor' => 'required',
            'driver_id' => 'required|integer',
            'driver_name' => 'required|string|max:255',
            'supervisor_id' => 'required|integer',
            'supervisor_name' => 'required|string|max:255',
            'usage_date' => 'required|date',
            'task' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422); // Return errors if validation fails
        }

        // If validation passes, create the resource
        $usage = MachineFuelUsage::create($request->all());
        return response()->json($usage, 201);
    }

    /**
     * Store a newly created resource in storage.
     */
public function store(Request $request)
{
    $usage = MachineFuelUsage::create($request->all());
    return response()->json($usage, 201);
}

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $usage = MachineFuelUsage::find($id);
        
        if (!$usage) {
            return response()->json(['message' => 'No Usage for this machine found'], 406);
        }

        return response()->json($usage);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MachineFuelUsage $machineFuelUsage)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $usage = MachineFuelUsage::findOrFail($id);
        
        $usage->update($request->all());
        return response()->json($usage);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        MachineFuelUsage::findOrFail($id)->delete();
        return response()->json(['message' => 'Deleted successfully']);
    }

    // public function machineFuelUsage($distribution_id)
    // {
    //     $usage = MachineFuelUsage::where('distribution_id', $distribution_id)->get();

    //     if ($usage->isEmpty()) {
    //         return response()->json(['message' => 'No Fuel Usage Available'], 404);
    //     }

    //     return response()->json($usage);
    // }
    
  public function machineFuelUsage($machine_id)
{
    $usage = MachineFuelUsage::with('bulk.purchaseOrder', 'machines')
        ->where('machine_id', $machine_id)
        ->get()
        ->map(function ($item) {
            return [
                'id' => $item->id,
                // 'machine_id' => $item->machine_id,
                // 'bulk_id' => $item->bulk_id,
                // 'distribution_id' => $item->distribution_id,
                'fuel_quantity' => $item->fuel_quantity,
                'ratio_factor' => $item->ratio_factor,
                // 'driver_id' => $item->driver_id,
                'driver' => $item->driver_name,
                // 'supervisor_id' => $item->supervisor_id,
                'supervisor' => $item->supervisor_name,
                'usage_date' => $item->usage_date,
                'task' => $item->task,
                'initial_reading' => $item->initial_reading,
                'final_reading' => $item->final_reading,
                // 'created_at' => $item->created_at,
                // 'updated_at' => $item->updated_at,

                // Bulk details
                // 'bulk_description' => $item->bulk->description ?? null,
                // 'bulk_unit_measure' => $item->bulk->unit_measure ?? null,
                // 'bulk_quantity' => $item->bulk->quantity ?? null,
                'rate' => $item->bulk->rate ?? null,

                // Purchase Order details
                // 'purchase_order_id' => $item->bulk->purchaseOrder->id ?? null,
                // 'purchase_order_supplier' => $item->bulk->purchaseOrder->supplier ?? null,
                // 'purchase_order_requested_by' => $item->bulk->purchaseOrder->requested_by ?? null,
                'project' => $item->bulk->purchaseOrder->project ?? null,
                // 'purchase_order_location' => $item->bulk->purchaseOrder->location ?? null,
                // 'purchase_order_status' => $item->bulk->purchaseOrder->status_name ?? null,

                // Machine details
                // 'machine_name' => $item->machines->name ?? null,
                // 'machine_type' => $item->machines->type_name ?? null,
                // 'machine_category_id' => $item->machines->category_id ?? null,
                // 'machine_capacity' => $item->machines->capacity ?? null,
            ];
        });

    if ($usage->isEmpty()) {
        return response()->json(['message' => 'No Fuel Usage Available For this machine'], 404);
    }

    return response()->json($usage);
}

 public function bulkFuelDistribution($bulk_id)
{
    $usage = MachineFuelUsage::with('bulk.purchaseOrder', 'machines')
        ->where('bulk_id', $bulk_id)
        ->get()
        ->map(function ($item) {
            return [
                'id' => $item->id,
                // 'machine_id' => $item->machine_id,
                // 'bulk_id' => $item->bulk_id,
                // 'distribution_id' => $item->distribution_id,
                'fuel_quantity' => $item->fuel_quantity,
                'ratio_factor' => $item->ratio_factor,
                // 'driver_id' => $item->driver_id,
                'driver' => $item->driver_name,
                // 'supervisor_id' => $item->supervisor_id,
                'supervisor' => $item->supervisor_name,
                'usage_date' => $item->usage_date,
                'task' => $item->task,
                'initial_reading' => $item->initial_reading,
                'final_reading' => $item->final_reading,
                // 'created_at' => $item->created_at,
                // 'updated_at' => $item->updated_at,

                // Bulk details
                // 'bulk_description' => $item->bulk->description ?? null,
                // 'bulk_unit_measure' => $item->bulk->unit_measure ?? null,
                // 'bulk_quantity' => $item->bulk->quantity ?? null,
                'rate' => $item->bulk->rate ?? null,

                // Purchase Order details
                // 'purchase_order_id' => $item->bulk->purchaseOrder->id ?? null,
                // 'purchase_order_supplier' => $item->bulk->purchaseOrder->supplier ?? null,
                // 'purchase_order_requested_by' => $item->bulk->purchaseOrder->requested_by ?? null,
                'project' => $item->bulk->purchaseOrder->project ?? null,
                // 'purchase_order_location' => $item->bulk->purchaseOrder->location ?? null,
                // 'purchase_order_status' => $item->bulk->purchaseOrder->status_name ?? null,

                // Machine details
                'machine_name' => $item->machines->name ?? null,
                // 'machine_type' => $item->machines->type_name ?? null,
                // 'machine_category_id' => $item->machines->category_id ?? null,
                // 'machine_capacity' => $item->machines->capacity ?? null,
            ];
        });

    if ($usage->isEmpty()) {
        return response()->json(['message' => 'No Fuel Distribution for this bulk'], 404);
    }

    return response()->json($usage);
}

}
