<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GoodsDeliveryNote extends Model
{
    protected $fillable = [
        'gin_id',
        'date_received',
        'received_by_id',
    ];

    public function gin() {
        return $this->belongsTo(GoodIssueNote::class,'gin_id');
    }

    public function receivedBy() {
        return $this->belongsTo(User::class,'received_by_id');
    }

    public function goodsDeliveryNoteItems() {
        return $this->hasMany(GoodsDeliveryNoteItem::class,'goods_delivery_note_id');
    }
}
