<?php

namespace App\Http\Controllers;

use App\Models\DirectFillRatio;
use Illuminate\Http\Request;

class DirectFillRatioController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json(DirectFillRatio::all());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $DirectFillRatio = DirectFillRatio::create($request->all());

        return response()->json($DirectFillRatio, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(DirectFillRatio $DirectFillRatio)
    {
        return response()->json($DirectFillRatio);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DirectFillRatio $DirectFillRatio)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DirectFillRatio $DirectFillRatio)
    {
        $DirectFillRatio->update($request->all());

        return response()->json($DirectFillRatio);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DirectFillRatio $DirectFillRatio)
    {
        $DirectFillRatio->delete();

        return response()->json(['message' => 'DirectFillRatio deleted']);
    }
}
