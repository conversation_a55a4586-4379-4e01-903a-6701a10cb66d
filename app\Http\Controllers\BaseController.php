<?php

namespace App\Http\Controllers;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;

use Illuminate\Http\Request;

 abstract class BaseController extends Controller
{
 
    protected string $modelClass;
    public function index(): JsonResponse
    {
        $items = $this->modelClass::all();
        
        return response()->json([
            'data' => $items,
            'message' => 'Items retrieved successfully',
            'success' => true
        ]);
    }

   
    public function store(Request $request): JsonResponse
    {
        $validated = $this->validateRequest($request);
        
        $item = $this->modelClass::create($validated);
        
        
        return response()->json([
            'data' => $item,
            'message' => 'Item created successfully',
            'success' => true
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show($id): JsonResponse
    {
        $item = $this->modelClass::findOrFail($id);
        
        return response()->json([
            'data' => $item,
            'message' => 'Item retrieved successfully',
            'success' => true
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id): JsonResponse
    {
        $item = $this->modelClass::findOrFail($id);
        
        $validated = $this->validateRequest($request, $item);
        
        $item->update($validated);
        
        return response()->json([
            'data' => $item,
            'message' => 'Item updated successfully',
            'success' => true
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id): JsonResponse
    {
        $item = $this->modelClass::findOrFail($id);
        
        $item->delete();
        
        return response()->json([
            'message' => 'Item deleted successfully',
            'success' => true
        ]);
    }
       protected function validateRequest(Request $request, ?Model $model = null): array
    {
        // Default behavior: allow all inputs
        return $request->all();
    }
    
    
}
