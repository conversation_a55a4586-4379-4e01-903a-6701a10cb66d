<?php

namespace App\Http\Controllers;

use App\Models\GoodDeliveryNote;
use Illuminate\Http\Request;

class GoodDeliveryNoteController extends Controller
{
    public function index()
    {
        $deliveryNotes = GoodDeliveryNote::with([
            'receivedBy',
            'status',
            'items',
        ])->get();

        return response()->json($deliveryNotes);
    }

 public function store(Request $request)
    {
        $deliveryNote = GoodDeliveryNote::create($request->all());

        return response()->json([
            'data' => $deliveryNote,
            'message' => 'item created successfully',
            'success' => true
        ]);
    }

    public function show($id)
    {
        $deliveryNote = GoodDeliveryNote::with([
            'receivedBy',
            'status',
            'items',
        ])->findOrFail($id);

            return response()->json($deliveryNote);
    }

    public function update(Request $request, $id)
    {
        $deliveryNote = GoodDeliveryNote::find($id);

        if (!$deliveryNote) {
            return response()->json([
                'message' => 'item not found',
                'success' => false
            ], 404);
        }
         $deliveryNote->update($request->all());        
         return response()->json([
            'data' => $deliveryNote,
            'message' => 'item updated successfully',
            'success' => true
        ]);    
    }

     public function destroy($id) {  
        $deliveryNote = GoodDeliveryNote::find($id);
         if (!$deliveryNote) {
            return response()->json([
                'message' => 'item not found',
                'success' => false,  
            ], 404);  
        }  
        $deliveryNote->delete();  
        return response()->json([  
            'message' => 'item deleted successfully',  
            'success' => true,  
        ]);     
     }
}
