<?php

namespace App\Http\Controllers;

use App\Models\ItemUsage;
use App\Models\StoreItem;
use Illuminate\Http\Request;

class ItemUsageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
 public function index(Request $request)
{
    $query = ItemUsage::query();

    // filter by usage_type if present
    if ($request->has('type')) {
        $query->where('usage_type', $request->type);
    }

    // filter by category_id if present
    if ($request->has('category_id')) {
        $query->where('category_id', $request->category_id);
    }

    return response()->json($query->get());
}
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $itemUsage = ItemUsage::create($request->all());

        return response()->json($itemUsage, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ItemUsage $itemUsage)
    {
        return response()->json($itemUsage);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ItemUsage $itemUsage)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ItemUsage $itemUsage)
    {
        $itemUsage->update($request->all());

        return response()->json($itemUsage);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ItemUsage $itemUsage)
    {
        $itemUsage->delete();

        return response()->json(['message' => 'Item usage deleted']);
    }

    public function getByJobCardId(Request $request)
    {
        $items = ItemUsage::with('spare')->where('usage_type',$request->type)->where('category_id', $request->category_id)->orderBy('id','desc')->get();

        return response()->json($items);
    }



    }
