<?php

namespace App\Http\Controllers;

use App\Models\ItemUsage;
use App\Models\StoreItem;
use Illuminate\Http\Request;

class ItemUsageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
 public function index(Request $request)
{
    $query = ItemUsage::query();

    // filter by usage_type if present
    if ($request->has('type')) {
        $query->where('usage_type', $request->type);
    }

    // filter by category_id if present
    if ($request->has('category_id')) {
        $query->where('category_id', $request->category_id);
    }

    return response()->json($query->get());
}
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Validate required fields
            $request->validate([
                'item_id' => 'required|exists:item_lists,id',
                'quantity' => 'required|numeric|min:0.01',
            ]);

            // Deduct quantity from store inventory first
            $this->deductFromStoreInventory($request->all());

            // Create the item usage record
            $itemUsage = ItemUsage::create($request->all());

            return response()->json([
                'data' => $itemUsage,
                'message' => 'Item usage created and inventory updated successfully',
                'success' => true
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create item usage',
                'error' => $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(ItemUsage $itemUsage)
    {
        return response()->json($itemUsage);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ItemUsage $itemUsage)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ItemUsage $itemUsage)
    {
        $itemUsage->update($request->all());

        return response()->json($itemUsage);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ItemUsage $itemUsage)
    {
        $itemUsage->delete();

        return response()->json(['message' => 'Item usage deleted']);
    }

    public function getByJobCardId(Request $request)
    {
        $items = ItemUsage::with('spare')->where('usage_type',$request->type)->where('category_id', $request->category_id)->orderBy('id','desc')->get();

        return response()->json($items);
    }

    /**
     * Deduct quantity from store inventory when item usage is created
     */
    protected function deductFromStoreInventory($itemData)
    {
        // If store_id is provided, deduct from specific store
        if (isset($itemData['store_id'])) {
            $storeItem = StoreItem::where('item_id', $itemData['item_id'])
                                 ->where('store_id', $itemData['store_id'])
                                 ->first();

            if (!$storeItem) {
                throw new \Exception('Item not found in the specified store inventory');
            }

            // Check if there's sufficient quantity
            if ($storeItem->quantity < $itemData['quantity']) {
                throw new \Exception('Insufficient quantity in store. Available: ' . $storeItem->quantity . ', Required: ' . $itemData['quantity']);
            }

            // Deduct the quantity
            $storeItem->quantity -= $itemData['quantity'];
            $storeItem->save();

        } else {
            // If no store_id provided, find the first store item with sufficient quantity
            $storeItem = StoreItem::where('item_id', $itemData['item_id'])
                                 ->where('quantity', '>=', $itemData['quantity'])
                                 ->orderBy('quantity', 'desc')
                                 ->first();

            if (!$storeItem) {
                throw new \Exception('Insufficient quantity available in any store for this item. Required: ' . $itemData['quantity']);
            }

            // Deduct the quantity
            $storeItem->quantity -= $itemData['quantity'];
            $storeItem->save();
        }
    }
}
