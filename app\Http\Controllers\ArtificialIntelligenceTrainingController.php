<?php

namespace App\Http\Controllers;

use App\Models\ArtificialIntelligenceTraining;
use Illuminate\Http\Request;

class ArtificialIntelligenceTrainingController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json(ArtificialIntelligenceTraining::all());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $instruction = ArtificialIntelligenceTraining::create($request->all());
        return response()->json($instruction,201);  
    }

    /**
     * Display the specified resource.
     */
    public function show(ArtificialIntelligenceTraining $artificialIntelligenceTraining)
    {
        return response()->json($artificialIntelligenceTraining);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ArtificialIntelligenceTraining $artificialIntelligenceTraining)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
 public function update(Request $request, ArtificialIntelligenceTraining $artificialIntelligenceTraining)
{
    $artificialIntelligenceTraining->update($request->all());
    return response()->json($artificialIntelligenceTraining, 200);
}


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ArtificialIntelligenceTraining $artificialIntelligenceTraining)
    {
        $artificialIntelligenceTraining->delete();
        return response()->json(['message'=>'deleted Successfully'],204);
    }
}
