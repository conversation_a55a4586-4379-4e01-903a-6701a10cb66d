<?php

namespace App\Http\Controllers;

use App\Models\Vendor;
use Illuminate\Http\Request;

class VendorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json(Vendor::all());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $Vendor = Vendor::create($request->all());

        return response()->json($Vendor, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Vendor $Vendor)
    {
        return response()->json($Vendor);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Vendor $Vendor)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Vendor $Vendor)
    {
        $Vendor->update($request->all());

        return response()->json($Vendor);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vendor $request, $Vendor)
    {
        $Vendor->update($request->all());;

        return response()->json(['message' => 'Vendor deleted']);
    }
}
