<?php

namespace App\Http\Controllers;

use App\Models\GoodReceivedNote;
use App\Models\PurchaseOrder;
use App\Models\ApprovalCheck;
use App\Models\StoreItem;
use App\Models\User;
use App\Models\GrnItem;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;

class GoodReceivedNoteController extends Controller
{
    /**
     * Display a listing of the resource.
     */
public function index()
{
    $grn = GoodReceivedNote::with([
        'receiver',
        'order.project',
        'items.grnItems'
    ])
    ->whereHas('order', function ($query) {
        $query->where('deleted', 0)->where('status_name','approved');
    })
    ->get();

    return response()->json($grn);
}




    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $GoodReceivedNote = GoodReceivedNote::create($request->all());

        return response()->json($GoodReceivedNote, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(GoodReceivedNote $GoodReceivedNote)
    {
    $GoodReceivedNote->load('receiver');
    return response()->json($GoodReceivedNote);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(GoodReceivedNote $GoodReceivedNote)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    // public function update(Request $request, GoodReceivedNote $GoodReceivedNote)
    // {
    //     $GoodReceivedNote->update($request->all());

    //     return response()->json($GoodReceivedNote);
    // }
    
public function update(Request $request, GoodReceivedNote $GoodReceivedNote)
{
    // Update the GoodReceivedNote with all data from the request
    $GoodReceivedNote->update($request->input('remarkData'));

    // Process each itemData
    $items = $request->input('itemData');
    $storeItems = [];

    foreach ($items as $item) {
        $existing = StoreItem::where('item_id', $item['item_id'])->first();

        if ($existing) {
            $existing->quantity += $item['quantity'];
            $existing->save();
            $storeItems[] = $existing;
        } else {
            $storeItems[] = StoreItem::create($item);
        }
    }

    return response()->json([
        'note' => $GoodReceivedNote,
        'storeItems' => $storeItems,
        'message' => 'Note and items updated successfully',
        'success' => true
    ]);
}


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(GoodReceivedNote $GoodReceivedNote)
    {
        $GoodReceivedNote->delete();

        return response()->json(['message' => 'GoodReceivedNote deleted']);
    }

 public function getGrn(Request $request)
{
    $constant = [
        'grn_no' => $request->grn_no
    ];

    $data = GoodReceivedNote::find($request->grn_no);
    if (!$data) return response('GRN not found', 404);

    $user = User::find($data->user_id);
    $items = GrnItem::with('grnItems')->where('grn_id', $request->grn_no)->get();

       $pdf = PDF::loadView('grn', compact('constant', 'data', 'user', 'items'));

    // return $pdf->stream('grn.pdf');
     return $pdf->download('grn.pdf');
}

public function updateGrnItem(Request $request, GrnItem $grnItem)
{
    $grnItem->update($request->all());
    return response()->json($grnItem);
}

    

}
