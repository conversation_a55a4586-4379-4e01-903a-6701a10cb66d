<?php

namespace App\Http\Controllers;

use App\Models\ItemUsage;
use App\Models\Store;
use App\Models\StoreItem;
use Illuminate\Http\Request;

class StoreController extends Controller
{
    /**
     * Display a listing of the resource.
     */
 public function index(Request $request)
{
    $query = Store::with('project');

    if ($request->has('project_id')) {
        $query->where('project_id', $request->project_id);
    }

    $stores = $query->get()->map(function ($store) {
        return [
            'id' => $store->id,
            'name' => $store->name,
            'project' => optional($store->project)->short_name,
            'project_name' => optional($store->project)->project_name,
            'project_id' => optional($store->project)->id,
            'financial_year' => optional($store->project)->financial_year,
            'client' => optional($store->project)->client,
            'contract_number' => optional($store->project)->contract_number,

        ];
    });

    return response()->json($stores);
}

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $Store = Store::create($request->all());

        return response()->json($Store, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Store $Store)
    {
        return response()->json($Store);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Store $Store)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Store $Store)
    {
        $Store->update($request->all());

        return response()->json($Store);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Store $Store)
    {
        $Store->delete();

        return response()->json(['message' => 'Item list deleted']);
    }
    
public function getStoreItems(Request $request)
{
    $storeItems = StoreItem::with('items.usage','items.category')->where('store_id', $request->store_id)->orderBy('id','desc')->get();

    $data = $storeItems->map(function ($storeItem) {
        $item = $storeItem->items;

        $usedQuantity = $item->usage->sum('quantity') ?? 0;

        return [
            'id' => $storeItem->id,
            'store_id' => $storeItem->store_id,
            'item_id' => $item->id,
            'category_name'   => $item->category->name ?? null,
            'name' => $item->name .' - '. $item->description,
            'unit' => $item->unit,
            'total_quantity' => $storeItem->quantity,
            'used_quantity' => $usedQuantity,
            'balance_quantity' => $storeItem->quantity - $usedQuantity 
        ];
    });

    return response()->json($data);
}

  
}
