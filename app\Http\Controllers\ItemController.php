<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\PurchaseOrder;
use App\Models\ApprovalCheck;
use App\Models\GrnItem;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;

class ItemController extends Controller
{
    /**
     * Display a listing of the resource.
     */
public function index()
{
    $items = Item::
        with('purchaseOrder', 'machine')
        ->orderBy('id', 'desc')
        // ->take(30)
        ->get();

    // Check if no items are found
    if ($items->isEmpty()) {
        return response()->json(['message' => 'No items found'], 404);
    }

    // Map the items data
    $mappedItems = $items->map(function ($item) {
        $purchaseOrder = $item->purchaseOrder;
        $machine = $item->machine;
        return [
            'id' => $item->id,
            'machine_id' => $item->machine_id,
            'remark_id' =>$item->remark_id,
            'order_id' => $item->order_id,
            'budget_id' =>$item->budget_id,
            'item_id' =>$item->item_list_id,
            'project' => $purchaseOrder->project->short_name ?? null,
            'project_id' => $purchaseOrder ? $purchaseOrder->project_id : null, 
            'rate'=> $purchaseOrder ? $purchaseOrder->rate : null, 
            'date_created' => $purchaseOrder ? $purchaseOrder->date_created : null, 
            'description' => $item->description,
            'unit_measure' => $item->unit_measure,
            'quantity' => $item->quantity,
            'rate' => $item->rate,
            'initial'=>$item->initial_odometer_reading,
            'final'=>$item->final_odometer_reading,
            'category' => $item->category,
            'was_full' =>$item->was_full,
            'type'=>$machine->type_name??'no_type',
            'asset_name'=>$machine->name??'not_asset',
            'consuption'=>$machine->consuption_type??'not_machine',
            'created_at' => $item->created_at,
            'updated_at' => $item->updated_at,
            'status' => $purchaseOrder ? $purchaseOrder->status_name : null,
        ];
    });

    return response()->json($mappedItems);
}



    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $item = Item::create($request->all());
           GrnItem::create([
            'grn_id' => $request->grn_no ?? 0,
            'item_id'=>$item->id,
            'asset_id'=>$request->asset_id ?? 0,
        ]);

        return response()->json($item, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Item $item)
    {
    return response()->json($item);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Item $item)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Item $item)
    {
        $item->update($request->all());

        return response()->json($item);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Item $item)
    {
        $item->delete();

        return response()->json(['message' => 'Item deleted']);
    }

    /**
     * Fetch items by order_id.
     */
    // public function getByOrderId($orderId)
    // {
    //     $items = Item::with('machine')->where('order_id', $orderId)->get();

    //     return response()->json($items);
    // }
    
    public function getByOrderId($orderId)
{
    $items = Item::with('machine:id,type_name') // Load only id and type_name
        ->where('order_id', $orderId)
        ->get()
        ->map(function ($item) {
            return [
                'id' => $item->id,
                'remark_id' =>$item->remark_id,
                'machine_id' => $item->machine_id,
                'order_id' => $item->order_id,
                'item_list_id'=>$item->item_list_id,
                'category' => $item->category,
                'description'=>$item->description,
                'specification'=>$item->specification,
                'unit_measure' => $item->unit_measure,
                'quantity'=>$item->quantity,
                'rate' => $item->rate,
                // add other item fields as needed
                'machine' => [
                    'type' => $item->machine->type_name ?? null,
                ],
            ];
        });

    return response()->json($items);
}


    public function getDiesel(Request $request)
    {
        $query = Item::with('purchaseOrder')
         ->whereIn('description', ['Diesel', 'Petrol']);

        if ($request->has('bulk_id')) {
            $query->where('id', $request->bulk_id);
        }

        $items = $query->get()->map(function ($item) {
            return [
                'id' => $item->id,
                'order_id' => $item->order_id,
                'quantity' => $item->quantity,
                'rate' => $item->rate,
                'project'=> $item->purchaseOrder->project,
                'supplier'=> $item->purchaseOrder->supplier,
                'address'=> $item->purchaseOrder->address,
                'date'=> $item->purchaseOrder->date_created,
                'location'=>$item->purchaseOrder->location,
                'payment_terms'=> $item->purchaseOrder->payment_terms,
                // Add other fields as necessary
            ];
        });

        return response()->json($items);
    }

    public function getDieselCount(){
        $query = Item::where('description', 'Diesel');
        $items = $query->count();
        return response()->json(["bulk-count"=>$items]);
    }

    public function copexLpoPdf($orderId){
        $constants = [
            'message' => 'Purchase order PDF',
            'phone_1'=>'0713 000 777',
            'phone_2'=>'0657 111 555',
            'heading'=>'PURCHASE ORDER',
    ]; 
    $data = Item::where('order_id', $orderId)->get();
    $order = PurchaseOrder::with('vendor')->find($orderId);
    $approval = ApprovalCheck::where('purchase_order_id', $orderId)->first();
        $pdf = Pdf::loadView('copex_lpo_pdf', compact(['constants','data','order','approval']));
        return $pdf->stream('printout.pdf');
    }

    public function bushmanLpoPdf($orderId){
        $constants = [
            'message' => 'Purchase order PDF',
            'phone_1'=>'0713 000 777',
            'phone_2'=>'0657 111 555',
            'heading'=>'PURCHASE ORDER',
    ]; 
    $data = Item::where('order_id', $orderId)->get();
    $order = PurchaseOrder::find($orderId);
    $approval = ApprovalCheck::where('purchase_order_id', $orderId)->first();
        $pdf = Pdf::loadView('bushman_lpo_pdf', compact(['constants','data','order','approval']));
        return $pdf->stream('printout.pdf');
    }

public function getFuelSum($project_id)
{
    $diesel  = Item::with('purchaseOrder')
      ->whereHas('purchaseOrder', function ($query) use ($project_id) {
            $query->where('project_id', $project_id)
                  ->where('description', 'Diesel')
                    ->where('status_name', 'approved');
        })
        ->sum('quantity'); 

    $petrol  = Item::with('purchaseOrder')
      ->whereHas('purchaseOrder', function ($query) use ($project_id) {
            $query->where('project_id', $project_id)
                  ->where('description', 'Petrol')
                    ->where('status_name', 'approved');
        })
        ->sum('quantity'); 
        

    return response()->json(['diesel_sum' => $diesel, 'petrol_sum' => $petrol]);
}



public function getBudgetItemUsed($id)
{
    $items = Item::where('item_list_id', $id)->sum('quantity');
    return response()->json(['used_items' => $items]);
}




public function whatsappResponse(Request $request)
{
    $hub_challenge = $request->input('hub_challenge'); // or $request->query('hub_challenge')
    return response($hub_challenge, 200); // Plain text response
}

public function handleWhatsAppMessage(Request $request)
{
    $rawData = $request->getContent();
    $data = json_decode($rawData, true);

    $messageType = $data['entry'][0]['changes'][0]['value']['messages'][0]['type'] ?? null;
    $senderWaId = $data['entry'][0]['changes'][0]['value']['contacts'][0]['wa_id'] ?? 'N/A';
    $messageContent = null;
    $selectedId = null;

    if ($messageType === 'text') {
        $messageContent = $data['entry'][0]['changes'][0]['value']['messages'][0]['text']['body'] ?? null;
    } elseif ($messageType === 'interactive') {
        $selectedId = $data['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['list_reply']['id'] ?? null;
    }

    $endpoint = 'https://graph.facebook.com/v22.0/721816217672551/messages';
    $accessToken = 'EAAXZAN9ZAW4VoBOZBhohedZA7pfKZBfzMghywTumPW7yFY0ZAI7u6Kh9DFtai4usEnmKsOnMZBYisZAQ0ojZBtEpDC72XXNyf6qFmtFtTCX61hv3I2eZCdZAevM9ahsDXGcIhT8stZAoTGstgOsujK4G1pxcSGqwFxiucZBcLr8HUoRxTLlB0IdVlLLlRGQ7n831T93WGUQZDZD';

    $client = new \GuzzleHttp\Client();

if (in_array($selectedId, ['END', 'END1', 'END2'])) {
    $client->post($endpoint, [
        'headers' => [
            'Authorization' => 'Bearer ' . $accessToken,
            'Content-Type' => 'application/json',
        ],
        'body' => json_encode([
            'messaging_product' => 'whatsapp',
            'to' => $senderWaId,
            'type' => 'text',
            'text' => [
                'body' => 'Your ticket is being processed. Thank you!'
            ]
        ])
    ]);
}
elseif ($selectedId === 'DAR-MORO') {
        $sourceOptionsPayload = [
            "messaging_product" => "whatsapp",
            "recipient_type" => "individual",
            "to" => $senderWaId,
            "type" => "interactive",
            "interactive" => [
                "type" => "list",
                "body" => [
                    "text" => "Select your level class"
                ],
                "action" => [
                    "button" => "Bus Class",
                    "sections" => [
                        [
                            "title" => "Available Classes",
                            "rows" => [
                                ["id" => "END", "title" => "FIRST CLASS", "description" => "13,000/="],
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $client->post($endpoint, [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode($sourceOptionsPayload)
        ]);
    }elseif ($selectedId === 'DMZ') {
        $sourceOptionsPayload = [
            "messaging_product" => "whatsapp",
            "recipient_type" => "individual",
            "to" => $senderWaId,
            "type" => "interactive",
            "interactive" => [
                "type" => "list",
                "body" => [
                    "text" => "Select your level class"
                ],
                "action" => [
                    "button" => "Bus Class",
                    "sections" => [
                        [
                            "title" => "Available Classes",
                            "rows" => [
                                ["id" => "END", "title" => "FIRST CLASS", "description" => "75,000/="],
                                ["id" => "END1", "title" => "SECOND CLASS", "description" => "70,000/="],
                                ["id" => "END2", "title" => "THIRD CLASS", "description" => "65,000/="],
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $client->post($endpoint, [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode($sourceOptionsPayload)
        ]);
    }elseif ($selectedId === 'DAR') {
        $sourceOptionsPayload = [
            "messaging_product" => "whatsapp",
            "recipient_type" => "individual",
            "to" => $senderWaId,
            "type" => "interactive",
            "interactive" => [
                "type" => "list",
                "body" => [
                    "text" => "Select your destination Region"
                ],
                "action" => [
                    "button" => "Source Locations",
                    "sections" => [
                        [
                            "title" => "Available Destinations",
                            "rows" => [
                                ["id" => "DAR-MORO", "title" => "MOROGORO"],
                                ["id" => "DMZ", "title" => "MWANZA"]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $client->post($endpoint, [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode($sourceOptionsPayload)
        ]);
    } elseif ($selectedId === 'MORO') {
        $sourceOptionsPayload = [
            "messaging_product" => "whatsapp",
            "recipient_type" => "individual",
            "to" => $senderWaId,
            "type" => "interactive",
            "interactive" => [
                "type" => "list",
                "body" => [
                    "text" => "Select your destination Region"
                ],
                "action" => [
                    "button" => "Destination Locations",
                    "sections" => [
                        [
                            "title" => "Available Destinations",
                            "rows" => [
                                ["id" => "END", "title" => "DAR"],
                                ["id" => "END1", "title" => "MWANZA"]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $client->post($endpoint, [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode($sourceOptionsPayload)
        ]);
    } elseif ($messageContent) {
        $interactivePayload = [
            "messaging_product" => "whatsapp",
            "recipient_type" => "individual",
            "to" => $senderWaId,
            "type" => "interactive",
            "interactive" => [
                "type" => "list",
                "header" => [
                    "type" => "text",
                    "text" => "ABOOD BUS BOOKING"
                ],
                "body" => [
                    "text" => "Select Source Region:"
                ],
                "action" => [
                    "button" => "Available Routes",
                    "sections" => [
                        [
                            "title" => "Booking Steps",
                            "rows" => [
                                [
                                    "id" => "DAR",
                                    "title" => "1.DAR-ES-SALAAM"
                                ],
                                [
                                    "id" => "DAR-MORO",
                                    "title" => "2. MOROGORO"
                                ]
                                // [
                                //     "id" => "TANGA",
                                //     "title" => "3. TANGA"
                                // ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $client->post($endpoint, [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode($interactivePayload)
        ]);
    }

    return response()->json(['status' => 'success']);
}




public function viewLogs()
{
    $logPath = resource_path('views/response.log');
    return response()->json(
        file_exists($logPath) ? file($logPath, FILE_IGNORE_NEW_LINES) : []
    );
}

}
