<?php

namespace App\Http\Controllers;

use App\Models\Remark;
use Illuminate\Http\Request;

class RemarkController extends Controller
{
    /**
     * Display a listing of the resource.
     */
public function index(Request $request)
{
    $remarkType = $request->input('remark_type');

    $remarks = Remark::with('sender')
        ->when($remarkType, function ($query, $remarkType) {
            return $query->where('remark_type', $remarkType);
        })
        ->get();

    $transformed = $remarks->map(function ($remark) {
        return [
            'id' => $remark->id,
            'description' => $remark->description,
            'remark_type' => $remark->remark_type,
            'type_id' => $remark->type_id,
            'remarker_id' => $remark->remarker_id,
            'status' => $remark->status,
            'created_at' => $remark->created_at,
            'updated_at' => $remark->updated_at,
            'sender_name' => optional($remark->sender)->name,
        ];
    });

    $uncheckedCount = $remarks->where('status', 'unchecked')->count();

    return response()->json([
        'data' => $transformed,
        'unchecked_count' => $uncheckedCount,
    ]);
}



    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $Remark = Remark::create($request->all());

        return response()->json($Remark, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Remark $Remark)
    {
        return response()->json($Remark);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Remark $Remark)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Remark $Remark)
    {
        $Remark->update($request->all());

        return response()->json($Remark);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Remark $Remark)
    {
        $Remark->delete();

        return response()->json(['message' => 'Remark deleted']);
    }
    
public function getUsageRemark($id){
    $remark = Remark::with('sender')
        ->where('type_id', $id)
        ->get()
        ->map(function($r) {
            return [
                'id' => $r->id,
                'description' => $r->description,
                'type_id' => $r->type_id,
                'remarker_id' => $r->remarker_id,
                'remark_type' =>$r->remark_type,
                'status' => $r->status,
                'created_at' => $r->created_at,
                'updated_at' => $r->updated_at,
                'sender_id' => $r->sender->id ?? null,
                'sender_name' => $r->sender->name ?? null,
                'sender_email' => $r->sender->email ?? null,
                'sender_role' => $r->sender->role ?? null,
            ];
        });

    return response()->json($remark);
}


public function checkRemark(Request $request)
{
    $remarkType = $request->input('remark_type');

    if (!$remarkType) {
        return response()->json(['message' => 'remark_type is required'], 400);
    }

    Remark::where('remark_type', $remarkType)->update(['status' => 'checked']);

    return response()->json(['message' => 'remark checked']);
}

public function getMessageGroups() {
    $remarks = Remark::with('sender')->get();

    $i = 1; // Start your counter outside map

    $groups = $remarks->groupBy('remark_type')->map(function ($items, $type) use (&$i) {
        $last = $items->last();
        $lastTime = $last->created_at;

        // Format time nicely
        if ($lastTime->isToday()) {
            $formattedTime = $lastTime->format('h:i A');
        } elseif ($lastTime->isYesterday()) {
            $formattedTime = 'Yesterday';
        } elseif ($lastTime->isSameYear(now())) {
            $formattedTime = $lastTime->format('F j');
        } else {
            $formattedTime = $lastTime->format('Y-m-d');
        }

        return [
            'no' => $i++, // Now increments properly
            'remark_type' => $type,
            'unchecked' => $items->where('status', 'unchecked')->count(),
            'last_message' => $last->description,
            'messages' => $items,
            'total_messages' => $items->count(),
            'last_time' => $formattedTime,
        ];
    })->values();

    return response()->json([
        'totalGroups' => $groups,
        'groupCount' => $groups->count(),
    ]);
}




}
