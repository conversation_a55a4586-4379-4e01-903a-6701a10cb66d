<?php

namespace App\Http\Controllers;

use App\Models\RequestLabourType;
use Illuminate\Http\Request;

class RequestLabourTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
public function index(Request $request)
{
    $query = RequestLabourType::with('labourType');

    if ($request->has('request_id')) {
        $query->where('labour_request_id', $request->request_id);
    }

    return response()->json($query->get());
}


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $RequestLabourType = RequestLabourType::create($request->all());

        return response()->json($RequestLabourType, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(RequestLabourType $RequestLabourType)
    {
        return response()->json($RequestLabourType);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RequestLabourType $RequestLabourType)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RequestLabourType $RequestLabourType)
    {
        $RequestLabourType->update($request->all());

        return response()->json($RequestLabourType);
    }

    /**
     * Remove the specified resource from storage.
     */
public function destroy($id)
{
    $labourType = RequestLabourType::find($id);

    if (!$labourType) {
        return response()->json(['message' => 'Item not found'], 404);
    }

    $labourType->delete();

    return response()->json(['message' => 'Item deleted']);
}


}
