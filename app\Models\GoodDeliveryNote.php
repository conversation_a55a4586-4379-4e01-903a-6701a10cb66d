<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GoodDeliveryNote extends Model
{
    protected $fillable = [
        'issue_note_id',
        'date_received',
        'received_by_id',
        'status_id',
    ];

    public function receivedBy() {
        return $this->belongsTo(User::class,'received_by_id');
    }

    public function status() {
        return $this->belongsTo(Status::class,'status_id');
    }

    public function items() {
        return $this->hasMany(GoodDeliveryNoteItem::class,'delivery_note_id');
    }

  
}
