<?php

namespace App\Http\Controllers;

use App\Models\RequisitionType;
use Illuminate\Http\Request;

class RequisitionTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $itemListCategories = RequisitionType::all();
        return response()->json($itemListCategories);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $RequisitionType = RequisitionType::create($request->all());

        return response()->json($RequisitionType, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(RequisitionType $RequisitionType)
    {
        return response()->json($RequisitionType);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RequisitionType $RequisitionType)
    {
        // This method is not typically used in API controllers
        // as APIs usually do not have a UI for editing.
        return response()->json($RequisitionType);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RequisitionType $RequisitionType)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $RequisitionType->update($request->all());

        return response()->json($RequisitionType);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RequisitionType $RequisitionType)
    {
        $RequisitionType->delete();

        return response()->json("Deleted Successfully", 204);
    }
}
