<?php

namespace App\Http\Controllers;

use App\Models\StoreItemCategory;
use Illuminate\Http\Request;

class StoreItemCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    
    public function index()
    {
        $itemListCategories = StoreItemCategory::all();
        return response()->json($itemListCategories);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $StoreItemCategory = StoreItemCategory::create($request->all());

        return response()->json($StoreItemCategory, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(StoreItemCategory $StoreItemCategory)
    {
        return response()->json($StoreItemCategory);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(StoreItemCategory $StoreItemCategory)
    {
        // This method is not typically used in API controllers
        // as APIs usually do not have a UI for editing.
        return response()->json($StoreItemCategory);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, StoreItemCategory $StoreItemCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $StoreItemCategory->update($request->all());

        return response()->json($StoreItemCategory);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(StoreItemCategory $StoreItemCategory)
    {
        $StoreItemCategory->delete();

        return response()->json("Deleted Successfully", 204);
    }
}
