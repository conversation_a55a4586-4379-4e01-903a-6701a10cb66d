<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Voucher extends Model
{
    protected $fillable = [
        'company_id',
        'receiver',
        'description',
        'user_id',
        'amount',
        'payment_type',
        'status_name',
        'status_color',
        'date_created'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
