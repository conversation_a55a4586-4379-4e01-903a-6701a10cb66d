<?php

namespace App\Http\Controllers;

use App\Models\Image;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ImageController extends Controller
{

     public function index()
    {
       $images = Image::all()->map(function ($img) {
        return [
            'id' => $img->id,
            'title' => $img->title,
            'url' => asset('storage/' . $img->image_path), // public URL
            'created_at' => $img->created_at,
            'updated_at' => $img->updated_at,
        ];
    });

    return response()->json($images);
}

    public function show($id)
    {
        $image = Image::find($id);
        return response()->json([
            'id' => $image->id,
            'title' => $image->title,
            'url' => asset('storage/' . $image->image_path), // public URL
            'created_at' => $image->created_at,
            'updated_at' => $image->updated_at,
        ]);
    }
    public function store(Request $request)
    {
        $imagePath = $request->file('image')->store('images', 'public');
        $image = Image::create([
            'title' => $request->title,
            'image_path' => $imagePath,
        ]);

        return response()->json([
            'message' => 'Image uploaded successfully',
            'image' => $image,
            'url' => Storage::url($imagePath)
        ], 201);
    }

   
}