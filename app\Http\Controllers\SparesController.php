<?php

namespace App\Http\Controllers;

use App\Models\Spares;
use App\Models\ItemList;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class SparesController extends Controller
{
    public function index()
    {
        $items = ItemList::with('category')->where('category_id',4)->where('deleted',0)->get(); 
         return response()->json([
            'data' => $items,
            'message' => 'Items retrieved successfully ',
            'success' => true
        ]);
    }

 public function show($id)
{
    // $item = Spares::with('storeItems.item.purchaseOrder.vendor')->find($id);
    $item = ItemList::with('category')->find($id);

    if (!$item) {
        return response()->json([
            'message' => 'Item not found',
            'success' => false
        ], 404);
    }

    return response()->json([
        'data' => $item,
        'message' => 'Item retrieved successfully',
        'success' => true
    ]);
}

  public function store(Request $request)
{
    $data = $request->all();
    $data['category_id'] = 4;

    $item = ItemList::create($data);

    return response()->json([
        'data' => $item,
        'message' => 'Item created successfully',
        'success' => true
    ]);
}


    public function update(Request $request, $id)
    {
        $spares = ItemList::find($id);

        if (!$spares) {
            return response()->json([
                'message' => 'Item not found',
                'success' => false
            ], 404);
        }
        
       $spares->update([
        'name' => $request->name,
        'description' => $request->part_number,
        'unit' =>$request->unit
    ]);
    
    return response()->json([
        'data' => $spares,
        'message' => 'Item updated successfully',
        'success' => true
    ]);

    }

    public function destroy(Request $request,$id)
    {
        $spares = ItemList::find($id);

        if (!$spares) {
            return response()->json([
                'message' => 'Item not found',
                'success' => false
            ], 404);
        }

        $spares->update($request->all());

        return response()->json([
            'data' => $spares,
            'message' => 'Item deleted successfully',
            'success' => true
        ]);
    }
}
