<?php

namespace App\Http\Controllers;

use App\Models\BoqSection;
use Illuminate\Http\Request;

class BoqSectionController extends Controller
{
    public function index()
    {
        $BoqSection = BoqSection::all();
        return response()->json($BoqSection);
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        // No validation here
        $BoqSection = BoqSection::create($request->all());
        return response()->json($BoqSection, 201);
    }

    public function show(BoqSection $BoqSection)
    {
        return response()->json($BoqSection);
    }

    public function edit(BoqSection $BoqSection)
    {
        return response()->json($BoqSection);
    }

    public function update(Request $request, BoqSection $BoqSection)
    {
        // No validation here
        $BoqSection->update($request->all());
        return response()->json($BoqSection);
    }

    public function destroy(BoqSection $BoqSection)
    {
        $BoqSection->delete();
        return response()->json("Deleted Successfully", 204);
    }
}
