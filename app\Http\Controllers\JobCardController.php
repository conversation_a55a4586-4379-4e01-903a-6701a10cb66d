<?php

namespace App\Http\Controllers;

use App\Models\JobCard;
use Illuminate\Http\Request;
use App\Models\ItemUsage;
use App\Models\Tyre;
use App\Models\MaintainanceLog;

class JobCardController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
      return response()->json(JobCard::with('machine')->orderBy('id', 'desc')->get());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $jobCard = JobCard::create($request->all());

        return response()->json($jobCard, 201);
    }

    /**
     * Display the specified resource.
     */
 public function show(JobCard $jobCard)
{
    $jobCard->load('machine', 'tyres', 'maintainanceLogs', 'costNotes');
    return response()->json($jobCard);
}

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(JobCard $jobCard)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, JobCard $jobCard)
    {
        $jobCard->update($request->all());

        return response()->json($jobCard);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(JobCard $request, $jobCard)
    {
        $jobCard->update($request->all());;

        return response()->json(['message' => 'Job card deleted']);
    }
    
 public function getJobcardSummary(Request $request)
{
    $data = Jobcard::with(['tyres', 'maintainanceLogs', 'costNotes', 'spares'])
        ->where('machine_id', $request->asset_id)
        ->get()
        ->map(function ($jobcard) {
            $tyresSum = $jobcard->tyres->sum('rate');
            $sparesSum = $jobcard->spares->sum(function ($spare) {
                return $spare->quantity * $spare->rate;
            });
            $costNotesSum = $jobcard->costNotes->sum(function ($note) {
                return $note->quantity * $note->rate;
            });

            return [
                'id' => $jobcard->id,
                'date_created' => $jobcard->date_created,
                'service_type' => $jobcard->service_type,
                'status_name' => $jobcard->status_name,
                'tyres_sum_rate' => $tyresSum,
                'spares' => $sparesSum,
                'costnotes_sum' => $costNotesSum,
                'total_sum' => $tyresSum + $sparesSum + $costNotesSum
            ];
        });

    return response()->json($data);
}


}
