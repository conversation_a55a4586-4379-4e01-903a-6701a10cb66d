<?php

namespace App\Http\Controllers;

use App\Models\Tyre;
use Illuminate\Http\Request;

class TyreController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json(Tyre::all());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $Tyre = Tyre::create($request->all());

        return response()->json($Tyre, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Tyre $Tyre)
    {
        return response()->json($Tyre);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Tyre $Tyre)
    {
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Tyre $Tyre)
    {
        $Tyre->update($request->all());

        return response()->json($Tyre);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Tyre $Tyre)
    {
        $Tyre->delete();

        return response()->json(['message' => 'Item list deleted']);
    }
    
public function getJobcardTyre(Request $request)
{
    $tyre = Tyre::where('asset_id', $request->asset_id)
                ->where('jobcard_id', $request->jobcard_id)
                ->get();

    if ($tyre->isEmpty()) {
        $tyre = Tyre::where('asset_id', $request->asset_id)->get();
    }

    return response()->json($tyre);
}

}
