<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Distribution extends Model
{
    protected $table = 'distribution';

    protected $fillable = [
        'machine',
        'machine_id',
        'issuer',
        'issuer_id',
        'fuel_issued',
        'fuel_quantity',
        'bulk_id',
        'ratio',
        'ratio_factor',
        'date_provided',

    ];
    
    public function machines()
{
    return $this->belongsTo(Machines::class,'machine_id');
}

    public function items()
{
    return $this->belongsTo(Item::class,'bulk_id');
}

    public function issuer()
{
    return $this->belongsTo(User::class,'issuer_id');
}
}
