<?php

namespace App\Http\Controllers;

use App\Models\BoqSubSection;
use Illuminate\Http\Request;

class BoqSubSectionController extends Controller
{
    public function index()
    {
        $BoqSubSection = BoqSubSection::all();
        return response()->json($BoqSubSection);
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        // No validation here
        $BoqSubSection = BoqSubSection::create($request->all());
        return response()->json($BoqSubSection, 201);
    }

    public function show(BoqSubSection $BoqSubSection)
    {
        return response()->json($BoqSubSection);
    }

    public function edit(BoqSubSection $BoqSubSection)
    {
        return response()->json($BoqSubSection);
    }

    public function update(Request $request, BoqSubSection $BoqSubSection)
    {
        // No validation here
        $BoqSubSection->update($request->all());
        return response()->json($BoqSubSection);
    }

    public function destroy(BoqSubSection $BoqSubSection)
    {
        $BoqSubSection->delete();
        return response()->json("Deleted Successfully", 204);
    }
}
