<?php

namespace App\Http\Controllers;

use App\Models\GoodsDeliveryNote;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class GoodsDeliveryNoteController extends Controller
{
    
public function index(Request $request)
{
    $query = GoodsDeliveryNote::with('gin.items.item.item', 'receivedBy', 'goodsDeliveryNoteItems');

    if ($request->has('gin_id')) {
        $query->where('gin_id', $request->gin_id);
    }

    $goodsDeliveryNotes = $query->get();

    $data = $goodsDeliveryNotes->map(function ($note) {
        return [
            'id' => $note->id,
            'gin_id'=> $note->gin_id,
            'date_received' => $note->date_received,
            'received_by' => $note->receivedBy->name ?? null,
            'items' => optional($note->gin)->items?->map(function ($ginItem) use ($note) {
                $requisitionItem = $ginItem->item ?? null;
                $item = $requisitionItem->item ?? null;

                $deliveredItem = $note->goodsDeliveryNoteItems
                    ->where('gin_item_id', $ginItem->id)
                    ->first();

                return [
                    'inventory_item_id' => $item->id ?? null,
                    'item_name' => $item->name ?? null,
                    'description' => $item->description ?? null,
                    'requested_quantity' => $requisitionItem->quantity ?? 0,
                    'supplied_quantity' => $ginItem->quantity ?? 0,
                    'supplied_item_id'=> $ginItem->id ?? 0,
                    'received_quantity' => $deliveredItem->quantity ?? 0,
                ];
            }) ?? [],
        ];
    });

    return response()->json($data);
}



    public function store(Request $request)
    {
        $goodsDeliveryNote = GoodsDeliveryNote::create($request->all());
        return response()->json($goodsDeliveryNote, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $goodsDeliveryNote = GoodsDeliveryNote::with('gin','receivedBy','goodsDeliveryNoteItems')->find($id);

        if (!$goodsDeliveryNote) {
            return response()->json([
                'message' => 'Goods Delivery Note not found',
                'success' => false
            ], 404);
        }

        return response()->json($goodsDeliveryNote);
    }


      public function update(Request $request, $id)
    {
        $deliveryNote = GoodsDeliveryNote::find($id);

        if (!$deliveryNote) {
            return response()->json([
                'message' => 'item not found',
                'success' => false
            ], 404);
        }
         $deliveryNote->update($request->all());        
         return response()->json([
            'data' => $deliveryNote,
            'message' => 'item updated successfully',
            'success' => true
        ]);    
    }

     public function destroy($id) {  
        $deliveryNote = GoodsDeliveryNote::find($id);
         if (!$deliveryNote) {
            return response()->json([
                'message' => 'item not found',
                'success' => false,  
            ], 404);  
        }  
        $deliveryNote->delete();  
        return response()->json([  
            'message' => 'item deleted successfully',  
            'success' => true,  
        ]);     
     }
}
