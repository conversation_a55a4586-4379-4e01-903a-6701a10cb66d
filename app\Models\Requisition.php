<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Requisition extends Model
{
    protected $fillable = [
    'requisition_type_id',
    'requester_id',
    'date',
    'required_date',
    'attachment',
    'status_id'
    ];
    
    public function project(){
        return $this->belongsTo(Project::class);
    }
    
      public function requester(){
        return $this->belongsTo(User::class);
    }
    
      public function requestedItems(){
        return $this->hasMany(RequisitionItem::class);
    }
    
       public function status(){
        return $this->belongsTo(Status::class);
    }
    
       public function goodIssueNote(){
        return $this->hasOne(GoodIssueNote::class);
    }
    
    public function type(){
        return $this->hasOne(RequisitionType::class,'requisition_id');
    }
    
    
}
