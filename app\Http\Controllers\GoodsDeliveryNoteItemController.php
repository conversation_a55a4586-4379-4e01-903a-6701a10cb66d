<?php

namespace App\Http\Controllers;

use App\Models\GoodsDeliveryNoteItem;
use App\Models\StoreItem;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class GoodsDeliveryNoteItemController extends Controller
{
    
    public function index()
    {
        $goodsDeliveryNoteItems = GoodsDeliveryNoteItem::with('goodsDeliveryNote','ginItem.item.item')->get();
        return response()->json($goodsDeliveryNoteItems);
    }

   
public function store(Request $request)
{
    if ($request->has('items')) {
        DB::beginTransaction();
        try {
            $createdItems = [];
            
            if (count($request->items) > 50) {
                GoodsDeliveryNoteItem::insert($request->items);
                $ids = DB::table('goods_delivery_note_items')
                       ->orderBy('id', 'desc')
                       ->limit(count($request->items))
                       ->pluck('id');
                $createdItems = GoodsDeliveryNoteItem::whereIn('id', $ids)->get();
            } else {
                $createdItems = collect($request->items)->map(function ($item) {
                    return GoodsDeliveryNoteItem::create($item);
                });
            }
            
            foreach ($request->items as $itemData) {
                $this->updateStoreInventory($itemData);
            }
            
            DB::commit();
            return response()->json([
                'data' => $createdItems,
                'message' => count($request->items) . ' items created successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to create items',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    $item = GoodsDeliveryNoteItem::create($request->all());
    $this->updateStoreInventory($request->all());
    
    return response()->json(['data' => $item, 'message' => 'Item created successfully'], 201);
}

protected function updateStoreInventory($itemData)
{
    $existing = StoreItem::where('item_id', $itemData['item_id'])
                         ->where('store_id', $itemData['store_id'])
                         ->first();

    if ($existing) {
        $existing->quantity += $itemData['quantity'];
        $existing->save();
    } else {
        StoreItem::create([
            'item_id' => $itemData['item_id'],
            'store_id' => $itemData['store_id'],
            'quantity' => $itemData['quantity'],
        ]);
    }
}

   
    public function show($id)
    {
        $item = GoodsDeliveryNoteItem::with('goodsDeliveryNote','ginItem')->find($id);
        return response()->json($item);
    }

  
    public function update(Request $request, $id)
    {
        $item = GoodsDeliveryNoteItem::findOrFail($id);
        $item->update($request->all());
        return response()->json(['message' => 'Item updated successfully', 'data' => $item]);
    }

 public function destroy($id)
    {
        $item = GoodsDeliveryNoteItem::findOrFail($id);
        $item->delete();

        return response()->json(['message' => 'Item deleted successfully']);
    }
}
