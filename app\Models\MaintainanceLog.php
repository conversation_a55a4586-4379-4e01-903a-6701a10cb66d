<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MaintainanceLog extends Model
{
    protected $fillable = [
        'job_card_id',
        'issue_type',
        'date',
        'time',
        'description',
        'reported_by',
        'status_id',
    ];

    public function machine()
    {
        return $this->belongsTo(Machine::class);
    }

    public function status()
    {
        return $this->belongsTo(Status::class);
    }
}
