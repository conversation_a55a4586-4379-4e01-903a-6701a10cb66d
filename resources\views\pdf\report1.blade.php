<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Card Report</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <style>
        body { font-family: Arial, sans-serif; }
        .container { width: 100%; text-align: center; }
        .top{
            margin-top:-16px
        }
        .bordered-box {
            border: 1px solid black;
            border-radius: 10px;
            padding: 10px;
            margin-top: 20px;
        }
        td {
            padding-top: 20px;
        }
        .table-bordered {
            width: 100%;
            border-collapse: collapse;
        }
        .table-bordered th, 
        .table-bordered td {
            border: 1px solid black;
            padding: 5px;
        }
        .remarks-box {
            border: 1px solid #000;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            min-height: 50px;
        }
        .signature-line {
            border-top: 1px solid #000;
            width: 250px;
            display: inline-block;
            margin-top: 50px;
        }
        .small-text {
            font-size: 12px;
        }
    </style>
</head>
<body>
    <img src="copex.jpg" style="height:50px;position:absolute">
    <span style="margin-left: 85%; display: flex; align-items: center; border: 1px solid black; border-radius: 5px; padding-left: 5px;" class="small-text">
        <span style="display: block; font-weight: bold;">Job Card No:</span>
        <span style="margin-left: 5px;">{{ $jobInfo['jobNumber'] }}</span>
    </span>
      
    <div class="container">
        <h2>COPEX CONTRACTORS CO.LTD</h2>
        <p class="top ">Msamvu, P.O.BOX 678, Morogoro</p>
        <p class="top">Mobile, {{ $constants['phone_1']??'0713 000 777' }}, {{$constants['phone_2']??'0657 111 555'}}</p>
        <div style="position: absolute; right: 0; top: 80px;">
            <span style="font-size: 14px; display: block; font-weight: bold;">TIN: 103-826-519,</span>
            <span style="font-size: 14px; display: block; font-weight: bold;">VRN: 20-01-017530R</span>
         </div>
        <h3><u>JOB CARD REPORT</u></h3>
    </div>
    
    <div class="bordered-box">
        <h5>Jobcard Information</h5>
        <table class="table-bordered">
            <thead>
                <tr>
                    <th>Job No.</th>
                    <th>Vehicle</th>
                    <th>Date</th>
                    <th>Serv.Type</th>
                    <th>Crr.KM</th>
                    <th>Nxt.KM</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ $jobInfo['jobNumber'] }}</td>
                    <td>{{ $jobInfo['vehicle'] }}</td>
                    <td>{{ $jobInfo['date'] }}</td>
                    <td>{{ $jobInfo['serviceType'] }}</td>
                    <td>{{ $jobInfo['currentKm'] }}</td>
                    <td>{{ $jobInfo['nextKm'] }}</td>
                    <td>{{ $jobInfo['status'] }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="bordered-box">
        <h5>Defects Detected</h5>
        <div class="remarks-box">
            {{ $defectsDetected }}
        </div>
    </div>

    <div class="bordered-box">
        <h5>Work Carried Out</h5>
        <div class="remarks-box">
            {{ $workCarriedOut }}
        </div>
    </div>

    <div class="bordered-box">
        <h5>Tyres Used</h5>
        <table class="table-bordered">
            <thead>
                <tr>
                    <th>SNo.</th>
                    <th>Tyre No.</th>
                    <th>Rate</th>
                    <th>Date Changed</th>
                </tr>
            </thead>
            <tbody>
                @foreach($tyresUsed as $tyre)
                <tr>
                    <td>{{ $tyre['serialNumber'] }}</td>
                    <td>{{ $tyre['tyreNumber'] }}</td>
                    <td>{{ $tyre['rate'] }}</td>
                    <td>{{ $tyre['dateChanged'] }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="bordered-box">
        <h5>Spares Required</h5>
        <table class="table-bordered">
            <thead>
                <tr>
                    <th>SNo.</th>
                    <th>Spare</th>
                    <th>Unit</th>
                    <th>Quantity</th>
                    <th>Rate</th>
                    <th>Total</th>
                    <th>Description</th>
                </tr>
            </thead>
            <tbody>
                @foreach($sparesRequired as $spare)
                <tr>
                    <td>{{ $spare['serialNumber'] }}</td>
                    <td>{{ $spare['spare'] }}</td>
                    <td>{{ $spare['unit'] }}</td>
                    <td>{{ $spare['quantity'] }}</td>
                    <td>{{ $spare['rate'] }}</td>
                    <td>{{ $spare['total'] }}</td>
                    <td>{{ $spare['description'] }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="bordered-box">
        <h5>Labour Cost</h5>
        <table class="table-bordered">
            <thead>
                <tr>
                    <th>SNo.</th>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Rate</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach($labourCost as $labour)
                <tr>
                    <td>{{ $labour['serialNumber'] }}</td>
                    <td>{{ $labour['description'] }}</td>
                    <td>{{ $labour['quantity'] }}</td>
                    <td>{{ $labour['rate'] }}</td>
                    <td>{{ $labour['total'] }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="bordered-box">
        <h5>Electrical Works</h5>
        <table class="table-bordered">
            <thead>
                <tr>
                    <th>SNo.</th>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Rate</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach($electricalWorks as $work)
                <tr>
                    <td>{{ $work['serialNumber'] }}</td>
                    <td>{{ $work['description'] }}</td>
                    <td>{{ $work['quantity'] }}</td>
                    <td>{{ $work['rate'] }}</td>
                    <td>{{ $work['total'] }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="bordered-box">
        <h5>Engineering Works</h5>
        <table class="table-bordered">
            <thead>
                <tr>
                    <th>SNo.</th>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Rate</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach($engineeringWorks as $work)
                <tr>
                    <td>{{ $work['serialNumber'] }}</td>
                    <td>{{ $work['description'] }}</td>
                    <td>{{ $work['quantity'] }}</td>
                    <td>{{ $work['rate'] }}</td>
                    <td>{{ $work['total'] }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="bordered-box">
        <h5>Additional Works</h5>
        <table class="table-bordered">
            <thead>
                <tr>
                    <th>SNo.</th>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Rate</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach($additionalWorks as $work)
                <tr>
                    <td>{{ $work['serialNumber'] }}</td>
                    <td>{{ $work['description'] }}</td>
                    <td>{{ $work['quantity'] }}</td>
                    <td>{{ $work['rate'] }}</td>
                    <td>{{ $work['total'] }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="bordered-box">
        <h5>Total Cost Note</h5>
        <table class="table-bordered" style="width: 50%;">
            <tbody>
                <tr>
                    <td>Total Spares Cost</td>
                    <td>{{ $totalCosts['sparesCost'] }}</td>
                </tr>
                <tr>
                    <td>Total Tyres Cost</td>
                    <td>{{ $totalCosts['tyresCost'] }}</td>
                </tr>
                <tr>
                    <td>Total Body Works Cost</td>
                    <td>{{ $totalCosts['bodyWorksCost'] }}</td>
                </tr>
                <tr>
                    <td>Total Electrical Works Costs</td>
                    <td>{{ $totalCosts['electricalWorksCost'] }}</td>
                </tr>
                <tr>
                    <td>Total Labour Cost</td>
                    <td>{{ $totalCosts['labourCost'] }}</td>
                </tr>
                <tr>
                    <td>Total Engineering Works Cost</td>
                    <td>{{ $totalCosts['engineeringWorksCost'] }}</td>
                </tr>
                <tr>
                    <td>Total Additional Works Cost</td>
                    <td>{{ $totalCosts['additionalWorksCost'] }}</td>
                </tr>
                <tr>
                    <td>Total Cost</td>
                    <td>{{ $totalCosts['totalCost'] }}</td>
                </tr>
            </tbody>
        </table>
    </div>
    
</body>
</html>