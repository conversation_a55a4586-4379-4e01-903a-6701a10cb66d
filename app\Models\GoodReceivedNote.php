<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GoodReceivedNote extends Model
{
    protected $fillable = [
      'user_id',
      'asset_id',
      'order_id',
      'remarks'
    ];
 
 public function receiver(){
     return $this->belongsTo(User::class,'user_id');
 }
 
  public function order(){
     return $this->belongsTo(PurchaseOrder::class,'order_id');
 }
 
  public function items(){
     return $this->hasMany(GrnItem::class,'grn_id');
 }

}
