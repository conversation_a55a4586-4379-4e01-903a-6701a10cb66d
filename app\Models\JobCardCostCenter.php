<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JobCardCostCenter extends Model
{
    protected $fillable = [
        'job_card_id',
        'cost_center_id',
    ];

    public function jobCard() {
        return $this->belongsTo(JobCard::class,'job_card_id');
    }

    public function costCenter() {
        return $this->belongsTo(CostCenter::class,'cost_center_id');
    }
}
