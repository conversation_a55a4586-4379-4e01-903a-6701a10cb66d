<?php

namespace App\Http\Controllers;

use App\Models\BotService;
use Illuminate\Http\Request;

class BotServiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $botServices = BotService::all();
        return response()->json($botServices);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $botService = BotService::create($validatedData);

        return response()->json($botService, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(BotService $botService)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BotService $botService)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BotService $botService)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BotService $botService)
    {
        //
    }
}
