<?php

namespace App\Http\Controllers;

use App\Models\ApprovalRole;
use Illuminate\Http\Request;

class ApprovalRoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json(ApprovalRole::all());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $ApprovalRole = ApprovalRole::create($request->all());

        return response()->json($ApprovalRole, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ApprovalRole $ApprovalRole)
    {
        return response()->json($ApprovalRole);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ApprovalRole $ApprovalRole)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ApprovalRole $ApprovalRole)
    {
        $ApprovalRole->update($request->all());

        return response()->json($ApprovalRole);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ApprovalRole $ApprovalRole)
    {
        $ApprovalRole->delete();

        return response()->json(['message' => 'ApprovalRole deleted']);
    }

 

}
