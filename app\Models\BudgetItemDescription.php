<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BudgetItemDescription extends Model
{
    //



    protected $table = 'budget_item_descriptions'; // Specify the table name if it's different from the default 'budget_item_descriptions'

    public function budgetItem()
    {
        return $this->belongsTo(BudgetItem::class, 'budget_item_id');
    }
    public function project()
    {
        return $this->belongsTo(Project::class);
    }
    public function budget()
    {
        return $this->belongsTo(Budget::class);
    }
}
