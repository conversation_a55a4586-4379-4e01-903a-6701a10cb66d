<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LabourRequest extends Model
{
    protected $fillable = [
        'user_id',
        'project_id',
        'date',
        'days',
        'task',
        'status_name',
        'status_color'
    ];
    
    public function project(){
        return $this->belongsTo(Project::class);
    }
    
      public function approvalCheck()
{
    return $this->hasOne(ApprovalCheck::class, 'purchase_order_id')->where('type', 'labour_request');
}

 public function user()
{
    return $this->belongsTo(User::class);
}
}
