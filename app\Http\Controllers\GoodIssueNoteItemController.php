<?php

namespace App\Http\Controllers;

use App\Models\GoodIssueNoteItem;
use Illuminate\Http\Request;

class GoodIssueNoteItemController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $items = GoodIssueNoteItem::all();
        return response()->json($items);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */

public function store(Request $request)
{
    $payload = $request->all();

    foreach ($payload['itemData'] as $item) {
        GoodIssueNoteItem::create([
            'good_issue_note_id' => $payload['gin_id'],
            'requisition_item_id' => $item['item_id'],
            'quantity' => $item['quantity'],
        ]);
    }

    return response()->json(['message' => 'Items saved successfully'], 201);
}
    /**
     * Display the specified resource.
     */
    public function show(GoodIssueNoteItem $GoodIssueNoteItem)
    {
        return response()->json($GoodIssueNoteItem);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(GoodIssueNoteItem $GoodIssueNoteItem)
    {
        // This method is not typically used in API controllers
        // as APIs usually do not have a UI for editing.
        return response()->json($GoodIssueNoteItem);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, GoodIssueNoteItem $GoodIssueNoteItem)
    {

        $GoodIssueNoteItem->update($request->all());

        return response()->json($GoodIssueNoteItem);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(GoodIssueNoteItem $request , $GoodIssueNoteItem)
    {
        $GoodIssueNoteItem->update($request->all());;

        return response()->json("Deleted Successfully", 204);
    }
}
