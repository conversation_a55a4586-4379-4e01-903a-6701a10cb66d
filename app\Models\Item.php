<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Item extends Model
{
    protected $fillable = [
        'remark_id',
        'machine_id',
        'budget_id',
        'item_list_id',
        'order_id',
        'category',
        'description',
        'specification',
        'unit_measure',
        'quantity',
        'initial_odometer_reading',
        'final_odometer_reading',
        'rate',
        'was_full',
    ];

    public function purchaseOrder()
{
    return $this->belongsTo(PurchaseOrder::class, 'order_id');
}

    public function machine()
{
    return $this->belongsTo(Machines::class, 'machine_id');
}

    public function distribution()
{
    return $this->hasMany(Distribution::class, 'bulk_id');
}

   public function fuelUsage()
{
    return $this->hasMany(MachineFuelUsage::class, 'bulk_id');
}

}
