<?php

namespace App\Http\Controllers;

use App\Models\ServiceDescriptionCategory;
use Illuminate\Http\Request;

class ServiceDescriptionCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
                $serviceDescriptionCategories = ServiceDescriptionCategory::all();
        return response()->json($serviceDescriptionCategories);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        
        $serviceDescriptionCategory = ServiceDescriptionCategory::create($request->all());

        return response()->json($serviceDescriptionCategory, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ServiceDescriptionCategory $serviceDescriptionCategory)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ServiceDescriptionCategory $serviceDescriptionCategory)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ServiceDescriptionCategory $serviceDescriptionCategory)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ServiceDescriptionCategory $serviceDescriptionCategory)
    {
        //
    }
}
