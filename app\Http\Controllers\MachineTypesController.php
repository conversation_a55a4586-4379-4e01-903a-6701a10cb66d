<?php

namespace App\Http\Controllers;

use App\Models\MachineTypes;
use Illuminate\Http\Request;

class MachineTypesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return MachineTypes::all();
    }

    /**
     * Store a newly created resource in storage.
     */
public function store(Request $request)
{
    return MachineTypes::create($request->all());
}


    /**
     * Display the specified resource.
     */
    public function show(MachineTypes $machineType)
    {
        return $machineType;
    }

    /**
     * Update the specified resource in storage.
     */
public function update(Request $request, MachineTypes $machineType)
{
    $machineType->update($request->all());

    return $machineType;
}


    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MachineTypes $machineType)
    {
        $machineType->delete();

        return response()->json(['message' => 'Deleted successfully']);
    }
}
