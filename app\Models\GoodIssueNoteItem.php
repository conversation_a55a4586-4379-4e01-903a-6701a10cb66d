<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GoodIssueNoteItem extends Model
{
    protected $fillable = [
        'good_issue_note_id',
        'requisition_item_id',
        'quantity'
    ];
    
    public function gin(){
        return $this->belongsTo(GoodIssueNote::class,'good_issue_note_id');
    }
    
    public function item(){
        return $this->belongsTo(RequisitionItem::class,'requisition_item_id');
    }
}
