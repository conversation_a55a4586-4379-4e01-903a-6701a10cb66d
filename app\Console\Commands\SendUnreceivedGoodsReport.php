<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Mail\UnreceivedGoodsReportMail;

class SendUnreceivedGoodsReport extends Command
{
    protected $signature = 'report:send';
    protected $description = 'Send the unreceived goods report email';

    public function handle()
    {
        // Replace with recipient email(s)
        // Mail::to('<EMAIL>')->send(new UnreceivedGoodsReportMail());
        Mail::to('<EMAIL>')->send(new UnreceivedGoodsReportMail());
        $this->info('Email sent successfully!');
    }
}