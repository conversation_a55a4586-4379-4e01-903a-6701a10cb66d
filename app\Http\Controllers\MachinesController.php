<?php

namespace App\Http\Controllers;

use App\Models\Machines;
use Illuminate\Http\Request;

class MachinesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return Machines::with('category')->get();
        // return Machines::all();
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $machines = Machines::create($request->validate([
            'name' => 'required|string',
            'type_name' => 'required|string',
            'type_id' => 'required|integer',
            'ratio' => 'required|numeric',
            'category_id' =>' required',
            'capacity' => 'required|numeric',
            'reserve_litre' => 'required|numeric',
        ]));

        return response()->json($machines, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        return response()->json(Machines::with('category')->findOrFail($id));
    }

    /**
     * Update the specified resource in storage.
     */
public function update(Request $request, $id)
{
    $machine = Machines::find($id);

    if (!$machine) {
        return response()->json(['message' => 'Machine not found'], 404);
    }

    $machine->update($request->all());

    return response()->json($machine);
}

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request ,$id)
    {
        Machines::findOrFail($id)->update($request->all());;
        return response()->json(['message' => 'Deleted successfully']);
    }
    
    
    public function getMachineCount(){
        $totalCount = Machines::count();
       return response()->json(['totalMachines' => $totalCount]);
    } 

     public function machineConsumption(Request $request)
    {
        $machines = Machines::whereHas('jobcards')
            ->with(['jobcards.tyres', 'jobcards.itemUsages', 'jobcards.costNotes'])
            // ->where('id',$request->asset_id)
            ->get()
            ->map(function ($machine) {
                return [
                    'machine_id' => $machine->id,
                    'machine_name' => $machine->name ?? null,
                    'machine_type' => $machine->type_name ?? null,
                    'fuel_type' => $machine->fuel_type ?? null,
                    'jobcards' => $machine->jobcards
                        ->map(function ($jobcard) {
                            return [
                                'jobcard_id' => $jobcard->id,
                                'service_type' => $jobcard->service_type,
                                'status' => $jobcard->status->name ?? null,
                                'status_color' => $jobcard->status->color ?? null,
                                'date_created' => $jobcard->date_created,
                                'tyres' => $jobcard->tyres->map(fn($t) => [
                                    'id' => $t->id,
                                    'tyre_no' => $t->tyre_no,
                                    'rate' => $t->rate ?? 0,
                                    'date_changed' => $t->date_changed ?? null,
                                ]),
                                'item_usages' => $jobcard->itemUsages->filter(fn($i) => $i->quantity > 0)->map(fn($i) => [
                                    'id' => $i->id,
                                    'usage_type'=>$i->usage_type,
                                    'spare' => $i->item_name,
                                    'quantity' => $i->quantity ?? 0,
                                    'rate' => $i->rate ?? 0, 
                                ]),
                                'cost_notes' => $jobcard->costNotes->filter(fn($c) => $c->quantity > 0 && $c->deleted != 1)->map(fn($c) => [
                                    'id' => $c->id,
                                    'type'=>$c->type,
                                    'description' => $c->description,
                                    'quantity' => $c->quantity ?? 0,
                                    'rate' => $c->rate ?? 0,
                                ]),
                            ];
                        })
                        ->values(), 
                ];
            })
            ->filter(fn($machine) => $machine['jobcards']->isNotEmpty()) // exclude machines with no valid jobcards
            ->values();

        if ($machines->isEmpty()) {
            return response()->json([
                'message' => 'No machines with valid completed job cards found.'
            ], 404);
        }

        return response()->json($machines);
    }

}
