<?php

namespace App\Http\Controllers;

use App\Models\RequisitionItem;
use Illuminate\Http\Request;

class RequisitionItemController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $itemListCategories = RequisitionItem::all();
        return response()->json($itemListCategories);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $RequisitionItem = RequisitionItem::create($request->all());

        return response()->json($RequisitionItem, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(RequisitionItem $RequisitionItem)
    {
        return response()->json($RequisitionItem);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RequisitionItem $RequisitionItem)
    {
        // This method is not typically used in API controllers
        // as APIs usually do not have a UI for editing.
        return response()->json($RequisitionItem);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RequisitionItem $RequisitionItem)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $RequisitionItem->update($request->all());

        return response()->json($RequisitionItem);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RequisitionItem $RequisitionItem)
    {
        $RequisitionItem->delete();

        return response()->json("Deleted Successfully", 204);
    }
}
