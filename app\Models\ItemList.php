<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ItemList extends Model
{
    protected $fillable = [
        'category_id',
        'name',
        'description',
        'unit',
        'deleted',
        'deleted_by',
        'deleted_date'
    ];
    
    public function category(){
        return $this->belongsTo(StoreItemCategory::class,'category_id');
    }
    
    public function usage(){
        return $this->hasMany(ItemUsage::class,'item_id');
    }
}
