<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Machines extends Model
{
    protected $fillable = [
        'name',
        'type_name',
        'make',
        'model',
        'fuel_type',
        'type_id',
        'category_id',
        'ratio',
        'capacity',
        'reserve_litre',
        'consuption_type',
    ];
    
    public function category(){
        return $this->belongsTo(Category::class);
    }

    public function jobCards(){
        return $this->hasMany(JobCard::class,'machine_id');
    }
}
