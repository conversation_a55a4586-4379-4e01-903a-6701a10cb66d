<?php

namespace App\Http\Controllers;

use App\Models\BoqLine;
use Illuminate\Http\Request;

class BoqLineController extends Controller
{
    public function index()
    {
        $BoqLine = BoqLine::all();
        return response()->json($BoqLine);
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        // No validation here
        $BoqLine = BoqLine::create($request->all());
        return response()->json($BoqLine, 201);
    }

    public function show(BoqLine $BoqLine)
    {
        return response()->json($BoqLine);
    }

    public function edit(BoqLine $BoqLine)
    {
        return response()->json($BoqLine);
    }

    public function update(Request $request, BoqLine $BoqLine)
    {
        // No validation here
        $BoqLine->update($request->all());
        return response()->json($BoqLine);
    }

    public function destroy(BoqLine $BoqLine)
    {
        $BoqLine->delete();
        return response()->json("Deleted Successfully", 204);
    }
}
