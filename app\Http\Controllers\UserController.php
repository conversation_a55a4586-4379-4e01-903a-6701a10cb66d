<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
public function index()
{
   $users = User::with('roleData.rolePermissions.permissions')
        ->get()
        ->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'remember_token' => $user->remember_token,
                'permissions' => $user->roleData ? 
                    $user->roleData->rolePermissions
                        ->pluck('permissions.name')
                        ->filter()
                        ->values()
                        ->all() 
                    : []
            ];
        });

    return response()->json($users);
}
public function getUser()
{
    $users = User::with('roleData.rolePermissions.permissions')
        ->get()
        ->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
                'remember_token' => $user->remember_token,
                'permissions' => $user->roleData ? 
                    $user->roleData->rolePermissions
                        ->pluck('permissions.name')
                        ->filter()
                        ->values()
                        ->all() 
                    : []
            ];
        });

    return response()->json($users);
}
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'role' => 'required|integer',
                'email' => 'required|string|email|max:255|unique:users',
                'password' => 'required|string|min:6',
            ]);
    
            $user = User::create([
                'name' => $request->name,
                'role' => $request->role,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'remember_token' => Str::random(60), 
            ]);
    
            return response()->json($user, 201);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'message' => 'Validation Error',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Something went wrong',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Display the specified resource.
     */
public function show(string $id)
{
    $user = User::with('roleData.rolePermissions.permissions')
        ->findOrFail($id);
    
    $formattedUser = [
        'id' => $user->id,
        'name' => $user->name,
        'email' => $user->email,
        'role' => $user->role,
        'remember_token' => $user->remember_token,
        'permissions' => $user->roleData ? 
            $user->roleData->rolePermissions
                ->pluck('permissions.name')
                ->filter()
                ->values()
                ->all() 
            : []
    ];

    return response()->json($formattedUser);
}
    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $user = User::findOrFail($id);

        $request->validate([
            'name' => 'sometimes|string|max:255',
            'role' => 'sometimes|integer',
            'email' => 'sometimes|string|email|max:255|unique:users,email,' . $id,
            'password' => 'sometimes|string|min:6',
        ]);

        $user->update([
            'name' => $request->name ?? $user->name,
            'role' => $request->role ?? $user->role,
            'email' => $request->email ?? $user->email,
            'password' => $request->password ? Hash::make($request->password) : $user->password,
        ]);

        return response()->json($user);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $user->update($request->all());
        return response()->json(null, 204);
    }

public function login(Request $request)
{
    $request->validate([
        'email' => 'required|email',
        'password' => 'required',
    ]);
    
    $user = User::with('roleData.rolePermissions.permissions')
        ->where('email', $request->email)
        ->first();
    
    if ($user && Hash::check($request->password, $user->password)) {
        $formattedUser = [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'role' => $user->role,
            'path' => $user->roleData->path ?? null,
            'remember_token' => $user->remember_token,
            'permissions' => $user->roleData ? 
                $user->roleData->rolePermissions
                    ->pluck('permissions.name')
                    ->filter()
                    ->values()
                    ->all() 
                : []
        ];
        
        return response()->json([
            'token' => $user->remember_token,
            'user' => $formattedUser
        ], 200);
    }
    
    return response()->json(['message' => 'Invalid credentials'], 401);
}
    

}
