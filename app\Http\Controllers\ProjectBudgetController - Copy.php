<?php

namespace App\Http\Controllers;

use App\Models\BudgetItemDescription;
use App\Models\BudgetItem;
use App\Models\Budget;
use App\Models\Project;
use Illuminate\Http\JsonResponse;

class ProjectBudgetController extends Controller
{
    /**
     * Get project budget summary
     *
     * @param int $projectId
     * @return JsonResponse
     */
    public function getProjectBudgetSummary($projectId): JsonResponse
    {
        // Find the project
        $project = Project::findOrFail($projectId);

        // Get all budgets for the project
        $budgets = Budget::where('project_id', $projectId)->get();

        // Calculate total funded amount from budgets
        $totalFundedAmount = $budgets->sum('funded_amount');

        // Get all budget items for the project
        $budgetItems = BudgetItem::where('project_id', $projectId)->get();

        // Calculate total budget cost from budget items
        $totalBudgetCost = 0;
        
        foreach ($budgetItems as $budgetItem) {
            $descriptions = BudgetItemDescription::where('budget_item_id', $budgetItem->id)->get();
            foreach ($descriptions as $desc) {
                $totalBudgetCost = $desc->cost * ($desc->quantity * ($desc->quantity_2 ?? 1));
            }
        }

        // Calculate balance (project cost - total budget cost)
        $balance = $project->project_cost - $totalBudgetCost;

        // Prepare the response data
        $response = [
            'project_name' => $project->short_name,
            'budgets_count' => $budgets->count(),
            'project_cost' => $project->project_cost,
            'budget_cost' => $totalBudgetCost,
            'balance' => $balance,
            'total_funded_amount' => $totalFundedAmount,
        ];

        return response()->json($response);
    }

    /**
     * Get budget summary for all projects
     *
     * @return JsonResponse
     */
    public function getAllProjectsBudgetSummary(): JsonResponse
    {
        $projects = Project::all();
        $result = [];

        foreach ($projects as $project) {
            $budgets = Budget::where('project_id', $project->id)->get();
            $budgetItems = BudgetItem::where('project_id', $project->id)->get();

            $totalFundedAmount = $budgets->sum('funded_amount');
            $totalBudgetCost = 0;

            foreach ($budgetItems as $budgetItem) {
                $descriptions = BudgetItemDescription::where('budget_item_id', $budgetItem->id)->get();
                
                foreach ($descriptions as $desc) {
                    $totalBudgetCost += $desc->cost * ($desc->quantity * ($desc->quantity_2 ?? 1));
                }
            }

            $balance = $project->project_cost - $totalBudgetCost;

            $result[] = [
                'project_id' => $project->id,
                'project_name' => $project->short_name,
                'budgets_count' => $budgets->count(),
                'project_cost' => $project->project_cost,
                'budget_cost' => $totalBudgetCost,
                'balance' => $balance,
                'total_funded_amount' => $totalFundedAmount,
            ];
        }

        return response()->json($result);
    }
}