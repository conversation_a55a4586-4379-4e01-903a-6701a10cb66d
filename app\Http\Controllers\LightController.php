<?php

namespace App\Http\Controllers;
use App\Models\WhatsappSender;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Http;

class LightController extends Controller
{
public function switchLights(Request $request)
{
    $data = json_decode($request->getContent(), true);
    $senderWaId = $data['entry'][0]['changes'][0]['value']['contacts'][0]['wa_id'] ?? null;
    $senderName = $data['entry'][0]['changes'][0]['value']['contacts'][0]['profile']['name'] ?? 'Customer';
    $messageId = $data['entry'][0]['changes'][0]['value']['messages'][0]['id'] ?? null;
    $selectedButton = $data['entry'][0]['changes'][0]['value']['messages'][0]['interactive']['button_reply']['id'] ?? null;
    
    if ($senderWaId && $messageId) {
        $existing = WhatsappSender::where('phone', $senderWaId)->first();

        if ($existing) {
            $existing->update(['message_id' => $messageId]);
        } else {
            WhatsappSender::create([
                'phone' => $senderWaId,
                'message_id' => $messageId,
            ]);
            
                $this->sendWhatsAppMessage([
    "messaging_product" => "whatsapp",
    "to" => $senderWaId,
    "type" => "text",
    "text" => ["body" => "Karibu Sana Mgeni"]
]);

        }
    }
    
    
    if (isset($data['entry'][0]['changes'][0]['value']['messages'][0]['text'])) {
        $messageContent = strtolower($data['entry'][0]['changes'][0]['value']['messages'][0]['text']['body']);
        
  if (preg_match('/(taa|toch|bulb|light|mwanga)/i', $messageContent)) {
    $response = Http::get('https://trustdigital.space/api/iot/lights/1');
    if ($response->successful()) {
        $lightData = $response->json(); // Get the actual JSON response
        $status = $lightData['status']; // Get status from the correct location
        $message = $status == 1 ? "Light is ON" : "Light is OFF";
        // Send button reply to toggle light
        $this->sendWhatsAppMessage([
            "messaging_product" => "whatsapp",
            "recipient_type" => "individual",
            "to" => $senderWaId,
            "type" => "interactive",
            "interactive" => [
                "type" => "button",
                "body" => [
                    "text" => $message
                ],
                "action" => [
                    "buttons" => [
                        [
                            "type" => "reply",
                            "reply" => [
                                "id" => $status == 1 ? "light_0" : "light_1",
                                "title" => $status == 1 ? "Switch Off" : "Switch On"
                            ]
                        ]
                    ]
                ]
            ]
        ]);
    }
}else{

           $this->sendWhatsAppMessage([
    "messaging_product" => "whatsapp",
    "to" => $senderWaId,
    "type" => "text",
// "text" => [
//     "body" => "To switch lights on or off, type the word *Taa*, *Tochi*, *Bulb*, or *Mwanga*.\n\nThis is a preview of how it will work using a real light installed at your home.\nYou can have multiple lights set up and control them from anywhere in the country or the world via WhatsApp.\n\nFor setup, contact the ICT expert at +255698267662."
// ]

"text" => [
    "body" => "Kuwasha au kuzima taa, andika neno *Taa*, *Tochi*, *Bulb* au *Mwanga*.\n\nHii ni mfano wa jinsi itakavyofanya kazi ukiwa na taa halisi iliyofungwa nyumbani kwako.\n\nUnaweza kufunga taa nyingi utakavyo na kuziwasha au kuzima ukiwa mahali popote ndani au nje ya nchi kupitia WhatsApp.\n\nKwa mawasiliano, wasiliana na mtaalamu wa TEHAMA kupitia +255698267662."
]


]);
}

}
if (str_starts_with($selectedButton, 'light_')) {
    $status = explode('_', $selectedButton)[1];

    $update = Http::put('https://trustdigital.space/api/iot/lights/1', [
        'status' => (int) $status
    ]);

    $message = $update->successful()
        ? ($status == 1 ? "Light is now ON" : "Light is now OFF")
        : "Failed to change light status.";

    $this->sendWhatsAppMessage([
        "messaging_product" => "whatsapp",
        "recipient_type" => "individual",
        "to" => $senderWaId,
        "type" => "interactive",
        "interactive" => [
            "type" => "button",
            "body" => [
                "text" => $message
            ],
            "action" => [
                "buttons" => [
                    [
                        "type" => "reply",
                        "reply" => [
                            "id" => $status == 1 ? "light_0" : "light_1",
                            "title" => $status == 1 ? "Switch Off" : "Switch On"
                        ]
                    ]
                ]
            ]
        ]
    ]);
}
    

}



// Helper function to avoid repeating API calls
private function sendWhatsAppMessage(array $payload)
{
    $client = new \GuzzleHttp\Client();
    $endpoint = 'https://graph.facebook.com/v22.0/721816217672551/messages';
    $accessToken = 'EAAXZAN9ZAW4VoBOZBhohedZA7pfKZBfzMghywTumPW7yFY0ZAI7u6Kh9DFtai4usEnmKsOnMZBYisZAQ0ojZBtEpDC72XXNyf6qFmtFtTCX61hv3I2eZCdZAevM9ahsDXGcIhT8stZAoTGstgOsujK4G1pxcSGqwFxiucZBcLr8HUoRxTLlB0IdVlLLlRGQ7n831T93WGUQZDZD'; // 🔴 Replace with env('WHATSAPP_TOKEN')

    $client->post($endpoint, [
        'headers' => [
            'Authorization' => 'Bearer ' . $accessToken,
            'Content-Type' => 'application/json',
        ],
        'body' => json_encode($payload)
    ]);
}
    

    
    private function callGeminiAPI($prompt) {
    $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=AIzaSyAuGP7TxA6KPTmzSV3IF8WhTfzIKUfygHU';
    
    $data = [
        'contents' => [
            [
                'parts' => [
                    ['text' => $prompt]
                ]
            ]
        ]
    ];
    
    $options = [
        'http' => [
            'header'  => "Content-Type: application/json\r\n",
            'method'  => 'POST',
            'content' => json_encode($data),
        ],
    ];
    
    $context  = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    if ($result === FALSE) {
        return "Samahani, sijapata jibu la swali lako.";
    }
    
    $responseData = json_decode($result, true);
    
    // Extract the response text - this path might need adjustment based on actual API response structure
    return $responseData['candidates'][0]['content']['parts'][0]['text'] ?? "Samahani, sijaelewa jibu lililotumwa.";
}
    
}