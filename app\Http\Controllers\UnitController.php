<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use Illuminate\Http\Request;

class UnitController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $itemListCategories = Unit::all();
        return response()->json($itemListCategories);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $Unit = Unit::create($request->all());

        return response()->json($Unit, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Unit $Unit)
    {
        return response()->json($Unit);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Unit $Unit)
    {
        // This method is not typically used in API controllers
        // as APIs usually do not have a UI for editing.
        return response()->json($Unit);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Unit $Unit)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $Unit->update($request->all());

        return response()->json($Unit);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Unit $request , $Unit)
    {
        $Unit->update($request->all());;

        return response()->json("Deleted Successfully", 204);
    }
}
