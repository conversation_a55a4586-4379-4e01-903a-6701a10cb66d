<?php

namespace App\Http\Controllers;

use App\Models\JobCardMotorVehicleDetail;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class JobCardMotorVehicleDetailController extends Controller
{
    
    public function index()
    {
        $jobCards = JobCardMotorVehicleDetail::with('jobCardCostCenter')->get();
        return response()->json($jobCards);
    }

    public function store(Request $request)
    {
        $jobCardMotorVehicleDetail = JobCardMotorVehicleDetail::create($request->all());
        return response()->json($jobCardMotorVehicleDetail, 201);
    }

    public function show($id)
    {
        $jobCardMotorVehicleDetail = JobCardMotorVehicleDetail::with('jobCardCostCenter')->find($id);
        if (!$jobCardMotorVehicleDetail) {
            return response()->json([
                'message' => 'Store not found',
                'success' => false
            ], 404);
        }
        return response()->json($jobCardMotorVehicleDetail);
    }


    public function update(Request $request, $id)
    {
        $jobCardMotorVehicleDetail = JobCardMotorVehicleDetail::with('jobCardCostCenter')->find($id);        
        if (!$jobCardMotorVehicleDetail) {
            return response()->json([
                'message' => 'Store not found',
                'success' => false
            ], 404);
        }        
        $jobCardMotorVehicleDetail->update($request->all());
        return response()->json($jobCardMotorVehicleDetail);
    }


    public function destroy($id)
    {
        $jobCardMotorVehicleDetail = JobCardMotorVehicleDetail::find($id);        
        if (!$jobCardMotorVehicleDetail) {
            return response()->json([
                'message' => 'Store not found',
                'success' => false
            ], 404);        
        }        
        $jobCardMotorVehicleDetail->delete();        
        return response()->json($jobCardMotorVehicleDetail);                
    }
}
