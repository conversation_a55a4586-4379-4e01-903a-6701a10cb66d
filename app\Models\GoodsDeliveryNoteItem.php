<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class GoodsDeliveryNoteItem extends Model
{
    protected $fillable = [
        'goods_delivery_note_id',
        'gin_item_id',
        'quantity',
    ];

    public function goodsDeliveryNote() {
        return $this->belongsTo(GoodsDeliveryNote::class,'goods_delivery_note_id');
    }

    public function ginItem() {
        return $this->belongsTo(GoodIssueNoteItem::class,'gin_item_id');
    }
}
