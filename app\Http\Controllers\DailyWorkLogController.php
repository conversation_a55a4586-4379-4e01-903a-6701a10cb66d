<?php

namespace App\Http\Controllers;

use App\Models\DailyWorkLog;
use Illuminate\Http\Request;

class DailyWorkLogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json(DailyWorkLog::all());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        DailyWorkLog::create($request->all());
        return response()->json(['message' => 'Work  created successfully'], 201);
    }

    /**
     * Display the specified resource.
     */
 public function show(DailyWorkLog $dailyWorkLog)
{
    return response()->json($dailyWorkLog);
}


    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DailyWorkLog $dailyWorkLog)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DailyWorkLog $dailyWorkLog)
    {
        $dailyWorkLog->update($request->all());
        return response()->json([$dailyWorkLog,'message' => 'Work updated successfully'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
public function destroy(DailyWorkLog $dailyWorkLog)
{
    $dailyWorkLog->delete();
    return response()->json(['message' => 'Daily work log deleted successfully.']);
}

    
 public function getDayWorkLog(Request $request)
{
    $userId = $request->labour_id;
    $date = $request->work_date;

    $workLog = DailyWorkLog::with('labour')->where('labour_id', $userId)
                ->where('work_date', $date)
                ->first();

    return response()->json($workLog);
}

public function getLogsByRequestId(Request $request){
    $worklog = DailyWorkLog::with('labour')->where('request_id',$request->request_id)->get();
    return response()->json($worklog);
}

}
