<?php
use App\Http\Controllers\GrnController;
use App\Http\Controllers\JobcardRateController;
use App\Http\Controllers\SparesController;
use App\Http\Controllers\StoreItemsController;
use App\Http\Controllers\WhatsappTemplateController;
use App\Models\BudgetItemDescription;
use App\Models\Spares;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\JobCardController;
use App\Http\Controllers\JobCardVendorsController;
use App\Http\Controllers\JobCardCostCenterController;
use App\Http\Controllers\JobCardMotorVehicleDetailController;
use App\Http\Controllers\GoodDeliveryNoteController;
use App\Http\Controllers\GoodDeliveryNoteItemController;
use App\Http\Controllers\AnalysisController;
use App\Http\Controllers\BudgetItemDescriptionController;
use App\Http\Controllers\PurchaseOrderController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\ImageController;
use Illuminate\Http\Request;

use App\Http\Controllers\ItemController;
use App\Http\Controllers\ApprovalCheckController;
use App\Http\Controllers\ItemListController;
use App\Http\Controllers\ServiceTypeController;
use App\Http\Controllers\MaintainanceLogController;


use App\Http\Controllers\UserController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\DistributionController;
use App\Http\Controllers\MachinesController;
use App\Http\Controllers\MachineFuelUsageController;

use App\Http\Controllers\ItemUsageController;

use App\Http\Controllers\MachineTypesController;

use App\Http\Controllers\RemarkController;
use App\Http\Controllers\CompanyController;

use App\Http\Controllers\DirectFillRatioController;
use App\Http\Controllers\VendorController;

use App\Http\Controllers\ItemListCategoryController;
use App\Http\Controllers\VoucherController;
use App\Http\Controllers\WhatsappMessageController;
use App\Http\Controllers\ServiceDescriptionController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\PaymentMethodController;
use App\Http\Controllers\ServiceDescriptionCategoryController;

// 21st April 2025
use App\Http\Controllers\RoleController;
use App\Http\Controllers\RolePermissionController;
use App\Http\Controllers\PermissionController;

// 24 april 2025
use App\Http\Controllers\PassengerController;

// 02 june 2025
use App\Http\Controllers\LabourRequestController;
use App\Http\Controllers\LabourRequestListController;

// 09 june 2025
use App\Http\Controllers\DailyWorkLogController;
use App\Http\Controllers\LabourController;
use App\Http\Controllers\LabourTypeController;
use App\Http\Controllers\RequestLabourTypeController;


// 11 june 2025 
use App\Http\Controllers\UnitController;

// 21 june 2025
use App\Http\Controllers\BoqSeriesController;
use App\Http\Controllers\BoqSectionController;
use App\Http\Controllers\BoqSubSectionController;
use App\Http\Controllers\BoqLineController;


//june 25 2025
use App\Http\Controllers\GoodReceivedNoteController;
//1 st july 2025

//2 july 2025
use App\Http\Controllers\StoreItemController;


use App\Http\Controllers\AiController;
use App\Http\Controllers\ArtificialIntelligenceTrainingController;

//5 july 2025 
use App\Http\Controllers\WhatsappSenderController;

//8 july 2025
use App\Http\Controllers\DashboardController;

//10th july 2025
use App\Http\Controllers\TyreController;


//14 july 2025
use App\Http\Controllers\LightController;
//18 july 2025
use App\Http\Controllers\StoreItemCategoryController;

//21 july 2025
use App\Http\Controllers\RequisitionController;
use App\Http\Controllers\RequisitionItemController;
use App\Http\Controllers\RequisitionTypeController;

//22 july 2025
use App\Http\Controllers\StatusController;
//23 july 2025
use App\Http\Controllers\GoodIssueNoteController;
use App\Http\Controllers\GoodIssueNoteItemController;


// 25 july 2025  ( Ngugo's)
use App\Http\Controllers\GoodsDeliveryNoteController;
use App\Http\Controllers\GoodsDeliveryNoteItemController;
//04 aug 2025
use App\Http\Controllers\CostNoteController;
use App\Http\Controllers\CheckController;









Route::apiResource('whatsapp-templates', WhatsappTemplateController::class);
Route::get('grn/{id}/print', [GrnController::class, 'print'])->name('grn.print');
Route::apiResource('spares', SparesController::class);
Route::get('get-spares', [SparesController::class, 'getSpares']);
Route::apiResource('store-items', StoreItemsController::class);



Route::apiResource('job-cards', JobCardController::class);
Route::apiResource('job-card-vendors', JobCardVendorsController::class);
Route::apiResource('job-card-cost-centers', JobCardCostCenterController::class);
Route::apiResource('job-card-motor-vehicle-details', JobCardMotorVehicleDetailController::class);
Route::apiResource('goods-delivery-notes', GoodDeliveryNoteController::class);
Route::apiResource('good-delivery-note-items', GoodDeliveryNoteItemController::class);

Route::get('/analysis/{projectId}', [AnalysisController::class, 'getProjectBudgetSummary']);
Route::get('/analysis', [AnalysisController::class, 'getAllProjectsBudgetSummary']);
Route::apiResource('budget-item-descriptions', BudgetItemDescriptionController::class);

// from now on specify the date of any thing added here 


Route::apiResource('lpo', PurchaseOrderController::class);
Route::apiResource('store', StoreController::class);

Route::apiResource('images', ImageController::class);








Route::get('clear-log', function() {
    $logFile = public_path('log.txt');
    
    if (file_exists($logFile)) {
        file_put_contents($logFile, ''); // Clear the file
        return "log.txt has been cleared!";
    } else {
        return "log.txt not found!";
    }
});


Route::get('get-user',[UserController::class,'getUser']);

Route::prefix('v1')->group(function () {
    Route::apiResource('users', UserController::class);
    Route::post('user/login', [UserController::class, 'login']);

    Route::apiResource('projects', ProjectController::class);

    Route::get('order-items/{order_id}', [ItemController::class, 'getByOrderId']);
    Route::get('copex-lpo-pdf/{order_id}',[ItemController::class,'copexLpoPdf']);
    Route::get('bushman-lpo-pdf/{order_id}',[ItemController::class,'bushmanLpoPdf']);


// Purchase orders
    Route::apiResource('purchase-order', PurchaseOrderController::class);
    Route::get('purchase-order-count',[PurchaseOrderController::class,'purchaseOrderCount']);
    Route::get('project-purchase-order/{project_id}',[PurchaseOrderController::class,'projectPurchaseOrder']);
    Route::get('budget-purchase-orders/',[PurchaseOrderController::class,'budgetPurchaseOrder']);
    Route::apiResource('items', ItemController::class);
    Route::get('get-diesel',[ItemController::class,'getDiesel']);
    
    Route::apiResource('approval-check', ApprovalCheckController::class);
    Route::get('approval-check-status/{purchase_order_id}', [ApprovalCheckController::class, 'getByOrderId']);
    Route::get('wages-approval/{wages_id}', [ApprovalCheckController::class, 'getByWagesId']);
    //24 jult 2025
    Route::get('reject-order', [ApprovalCheckController::class, 'rejectOrder']);
    Route::apiResource('item-list', ItemListController::class);
    
    Route::apiResource('machines', MachinesController::class);
    Route::get('machine-count',[MachinesController::class,'getMachineCount']);
    
    Route::apiResource('distribution', DistributionController::class);
    Route::get('bulk_distribution/{bulk_id}', [DistributionController::class, 'bulkDistribution']);
        Route::get('machine_distribution/{machine_id}', [DistributionController::class, 'machineDistribution']);
    Route::get('diesel-statement-count',[DistributionController::class,'getDistributionCount']);  
    
      Route::apiResource('fuel-usage', MachineFuelUsageController::class);
      Route::get('machine-fuel-usage/{machine_id}', [MachineFuelUsageController::class,'machineFuelUsage']);
         Route::get('bulk-fuel-distribution/{bulk_id}', [MachineFuelUsageController::class,'bulkFuelDistribution']);
      
          Route::apiResource('service-type', ServiceTypeController::class);
            Route::apiResource('maintainance-log', MaintainanceLogController::class);
    Route::get('job-card-maintainance-log/{job_card_id}', [MaintainanceLogController::class, 'getByJobCardId']);
    Route::apiResource('job-card', JobCardController::class);
    
        Route::apiResource('spares', SparesController::class);
           Route::get('job-card-item-usage', [ItemUsageController::class, 'getByJobCardId']);
           
            Route::apiResource('item-usage', ItemUsageController::class);
   Route::apiResource('machine-types', MachineTypesController::class);
   
   Route::get('ai-source',[PurchaseOrderController::class,'purchaseOrderItems']);

    Route::apiResource('remark', RemarkController::class);
    Route::get('check-remark', [RemarkController::class,'checkRemark']);
    Route::get('usage-remark/{usage_id}',[RemarkController::class,'getUsageRemark']);
        Route::apiResource('company', CompanyController::class);
            Route::apiResource('direct-fill-ratio', DirectFillRatioController::class);
            Route::get('fuel-sum/{project_id}',[ItemController::class,'getFuelSum']);
                Route::apiResource('vendors', VendorController::class);
               Route::get('whatsapp-response', [ItemController::class, 'whatsappResponse']);
            //   Route::post('whatsapp-response', [ItemController::class, 'handleWhatsAppMessage']);
                //   Route::post('whatsapp-response', [WhatsappMessageController::class, 'getWhatsappAutoResponse']);
                  Route::post('whatsapp-response', [LightController::class, 'switchLights']);
                  Route::get('ticket-invoice/{senderWaId}',[WhatsappMessageController::class,'ticketInvoice']);
                //   Route::post('whatsapp-response', [WhatsappMessageController::class, 'servicePrototype']);
                   Route::get('get-whatsapp-controller', [WhatsappMessageController::class, 'getWhatsappController']);
                 
                        Route::apiResource('regions', WhatsappMessageController::class);
                         Route::get('dropping-region', [WhatsappMessageController::class,'droppingRegion']);  
               
               
               Route::get('view-logs', [ItemController::class, 'viewLogs']);
               Route::apiResource('item-list-category', ItemListCategoryController::class);
                   Route::apiResource('voucher', VoucherController::class);
                     Route::apiResource('service', ServiceController::class);
    Route::apiResource('service-description', ServiceDescriptionController::class);
        Route::apiResource('payment-gateway', PaymentMethodController::class);
               Route::apiResource('description-category', ServiceDescriptionCategoryController::class);
               
            //   21st April 2025
                    Route::apiResource('roles', RoleController::class);
                    Route::apiResource('permission', PermissionController::class);
                    Route::apiResource('role-permission', RolePermissionController::class);
                    Route::delete('role-permission/{roleId}/{permissionId}', [RolePermissionController::class, 'destroy'])
    ->name('role-permission.destroy');

      Route::apiResource('passengers', PassengerController::class);  
      
    //   02 june 2025
     Route::apiResource('labour-request', LabourRequestController::class);
     Route::apiResource('labour-request-list', LabourRequestListController::class);   
     
    //  09 june 2025
        Route::apiResource('daily-work-logs',DailyWOrkLogController::class);
        Route::apiResource('labours',LabourController::class);    
        Route::get('approved-labours',[LabourController::class,'approvedLabours']); 
        Route::apiResource('labour-types',LabourTypeController::class,);  
        Route::apiResource('request-labour-types',RequestLabourTypeController::class,); 
        Route::get('day-work-log',[DailyWOrkLogController::class,'getDayWorkLog']);      
        
        // 11 june 2025
        Route::apiResource('units',UnitController::class); 
        Route::get('items-used/{id}',[ItemController::class,'getBudgetItemUsed']);
        
        // 20 june 2025
        Route::get('message-groups',[RemarkController::class,'getMessageGroups']);
        
    //21 june 2025
     Route::get('boq',[BoqSeriesController::class,'getProjectBoq']);
     Route::apiResource('boq-series',BoqSeriesController::class); 
     Route::apiResource('boq-sections',BoqSectionController::class); 
     Route::apiResource('boq-sub-sections',BoqSubSectionController::class);
     Route::apiResource('boq-lines',BoqLineController::class);  
     
     //24 june 2025
      Route::get('labours-work',[DailyWOrkLogController::class,'getLogsByRequestId']); 
      Route::apiResource('good-received-notes',GoodReceivedNoteController::class);  
      
      //26 june 2025
      Route::get('grn-reports',[GoodReceivedNoteController::class,'getGrn']);  
     
     //28 june 2025
     Route::put('update-grn-item/{grnItem}', [GoodReceivedNoteController::class, 'updateGrnItem']);
//1 st january 2025
Route::apiResource('ai',AiController::class);
Route::get('get-answer', [AiController::class,'getAnswer']);
Route::apiResource('artificial-intelligence-training',ArtificialIntelligenceTrainingController::class);

//02 july 2025
Route::apiResource('store-items',StoreItemController::class);

//04 july 2025 
Route::post('create-store-lpo',[PurchaseOrderController::class,'createStoreLpo']);

//05 july 2025
Route::apiResource('whatsapp-senders', WhatsappSenderController::class); 

//08 july 2025
Route::prefix('dashboard')->group(function () {
  Route::get('summary', [DashboardController::class,'getSummary']); 
  //09 july 2025
  Route::get('lpos', [DashboardController::class,'getLpo']); 
  Route::get('unchecked-grn', [DashboardController::class,'getUncheckedGrn']); 
  //22 july 2025
  Route::get('jobcards', [DashboardController::class,'getJobCards']); 
  //24 july 2025
  Route::get('requisitions', [DashboardController::class,'getRequisitions']); 
  Route::get('gins', [DashboardController::class,'getGin']); 
});

//10 july 2025
Route::apiResource('tyres', TyreController::class); 
Route::get('jobcard-tyre', [TyreController::class,'getJobcardTyre']); 
Route::post('ai-answers', [AiController::class,'getAiAnswer']); 
//11 july 2025
Route::apiResource('stores', StoreController::class); 
Route::get('get-store-items', [StoreController::class,'getStoreItems']); 

//14 july 2025
Route::get('deleted-orders',[ PurchaseOrderController::class,'deletedOrders']);

//18 july 2025
Route::apiResource('store-item-categories', StoreItemCategoryController::class); 
Route::get('get-materials',[ ItemListController::class,'getMaterials']);


//21 july 2025
Route::apiResource('requisitions', RequisitionController::class); 
Route::apiResource('requisition-items', RequisitionItemController::class);
Route::apiResource('requisition-types', RequisitionTypeController::class); 

//22 july 2025
Route::apiResource('statuses', StatusController::class); 

//23 july 2025
Route::apiResource('good-issue-notes', GOodIssueNoteController::class); 
Route::apiResource('gin-items', GOodIssueNoteItemController::class); 

// 25 july 2025 ( Ngugo's)

Route::apiResource('goods-delivery-notes', GoodsDeliveryNoteController::class);
Route::apiResource('goods-delivery-note-items', GoodsDeliveryNoteItemController::class);
Route::get('ngugo', [GOodIssueNoteController::class,'ngugo']); 




//04 aug 2025
Route::apiResource('cost-notes', CostNoteController::class);
Route::get('cost-note-summary', [CostNoteController::class,'costNoteSummary']);
Route::post('jobcard-mail', [CostNoteController::class,'jobcardMail']);

//06 aug 2025
Route::apiResource('purchase-orders', PurchaseOrderController::class);

//Ngugo 06 aug 2025
Route::get('/analysis/{projectId}', [AnalysisController::class, 'getProjectBudgetSummary']);
Route::get('/analysis', [AnalysisController::class, 'getAllProjectsBudgetSummary']);
// Route::apiResource('budgets', BudgetController::class);
// Route::apiResource('budget-items', BudgetItemController::class);
Route::apiResource('budget-item-descriptions', BudgetItemDescriptionController::class);

//08 august 2025
Route::get('/jobcard-summary', [JobCardController::class, 'getJobCardSummary']);

//11 august 2025
Route::post('create-requisition',[RequisitionController::class,'createRequisition']);

});

Route::apiResource('jobcard-rate', JobcardRateController::class);
Route::apiResource('checks', CheckController::class);

// 22/08/2025
Route::get('machine-consumptions', [MachinesController::class,'machineConsumption']);